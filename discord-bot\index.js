require('dotenv').config();
const { Client, GatewayIntentBits, Collection, REST, Routes } = require('discord.js');
const express = require('express');
const Database = require('./database');
const PurchaseHandler = require('./handlers/purchaseHandler');
const LicenseAPI = require('./api/licenseAPI');
// const SupabaseLicenseAPI = require('./api/supabaseLicenseAPI'); // Removido - não usado diretamente
const configurarCommand = require('./commands/configurar');
const aprovarCommand = require('./commands/aprovar');

// Criar servidor web para manter o serviço ativo no Render
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

// Rota de health check
app.get('/', (req, res) => {
    res.json({
        status: 'online',
        service: 'StonePlugins Discord Bot',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Rota de status do bot
app.get('/status', (req, res) => {
    res.json({
        bot_status: client.isReady() ? 'online' : 'offline',
        guilds: client.guilds.cache.size,
        users: client.users.cache.size,
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// Rota de health check para o plugin (API)
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'StonePlugins License API',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        api_status: 'online'
    });
});

// Rota para validação de licenças
app.post('/api/license/validate', (req, res) => {
    const { license_key, server_uuid } = req.body;
    
    // Por enquanto, sempre retorna válido para testes
    // TODO: Implementar validação real com Supabase
    res.json({
        success: true,
        valid: true,
        license_key: license_key,
        server_uuid: server_uuid,
        message: 'License is valid'
    });
});

// Rota para ativação de licenças
app.post('/api/license/activate', (req, res) => {
    const { license_key, server_ip, server_port, server_uuid } = req.body;
    
    // Por enquanto, sempre retorna sucesso para testes
    // TODO: Implementar ativação real com Supabase
    res.json({
        success: true,
        product_name: 'StonePayments',
        expires_at: null, // Vitalícia
        message: 'License activated successfully'
    });
});

// Rota para notificação de shutdown do servidor
app.post('/api/server/shutdown', (req, res) => {
    const { server_uuid } = req.body;
    console.log(`🔌 Servidor desconectado: ${server_uuid}`);
    
    res.json({
        success: true,
        message: 'Shutdown notification received'
    });
});

// Iniciar servidor web
app.listen(PORT, () => {
    if (process.env.NODE_ENV !== 'production') {
        console.log(`🌐 Servidor web: http://localhost:${PORT}/`);
    }
});

// Criar cliente Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Inicializar componentes
console.log('🔄 Inicializando componentes do sistema...');
const database = new Database();
const purchaseHandler = new PurchaseHandler(database);
const licenseAPI = new LicenseAPI(database);
console.log('✅ Componentes inicializados');

// Inicializar sistema de backup e proteção (apenas para SQLite local)
let backupSystem = null;
let databaseProtection = null;
if (!process.env.DATABASE_URL && database.db) {
    try {
        const BackupSystem = require('./backup-system');
        const DatabaseProtection = require('./database-protection');

        backupSystem = new BackupSystem(database);
        databaseProtection = new DatabaseProtection(database);

        console.log('💾 Sistema de backup inicializado (SQLite local)');
        console.log('🛡️ Sistema de proteção de dados inicializado');
    } catch (error) {
        console.log('⚠️ Sistemas de backup não disponíveis (normal em produção)');
    }
} else if (process.env.DATABASE_URL) {
    console.log('🐘 PostgreSQL detectado - backup gerenciado pelo Render');
} else {
    console.log('☁️ Usando apenas Supabase - sem backup local');
}

// Disponibilizar database globalmente para comandos
client.database = database;

// Coleção de comandos
client.commands = new Collection();
client.commands.set('configurar', configurarCommand);
client.commands.set('aprovar', aprovarCommand);

// Configurar API de licenças no servidor web (antes do bot iniciar)
try {
    licenseAPI.setupRoutes(app);
    const baseUrl = process.env.RENDER_EXTERNAL_URL || `http://localhost:${PORT}`;
    console.log(`🚀 Sistema pronto: ${baseUrl}`);
} catch (error) {
    console.error('❌ Erro ao configurar License API:', error);
}

// Evento: Bot pronto
client.once('ready', async () => {
    try {
        console.log(`✅ Bot online: ${client.user.tag} | ${client.guilds.cache.size} servidor(es)`);

        // Registrar comandos slash
        await registerSlashCommands();

        // Definir status
        client.user.setActivity('Sistema de Licenças | /configurar', { type: 'WATCHING' });

        console.log(`🎉 Sistema operacional!`);
    } catch (error) {
        console.error('❌ Erro no evento ready:', error);
        console.error('Stack trace:', error.stack);
    }

    // Iniciar limpeza periódica de compras antigas
    setInterval(() => {
        purchaseHandler.cleanupOldPurchases();
    }, 5 * 60 * 1000); // A cada 5 minutos

    // Iniciar limpeza periódica de vendas antigas no Supabase
    const { SupabaseLicenseManager } = require('./supabase');
    const supabaseLicenseManager = new SupabaseLicenseManager();

    // Limpeza inicial
    try {
        await supabaseLicenseManager.cleanupOldSales();
    } catch (error) {
        console.error('❌ Erro na limpeza inicial de vendas:', error);
    }

    // Limpeza periódica a cada 1 hora
    setInterval(async () => {
        try {
            await supabaseLicenseManager.cleanupOldSales();
        } catch (error) {
            console.error('❌ Erro na limpeza periódica de vendas:', error);
        }
    }, 60 * 60 * 1000); // A cada 1 hora

    // Atualizar canais de status a cada 5 minutos
    setInterval(async () => {
        try {
            await updateStatusChannels(client);
        } catch (error) {
            console.error('❌ Erro ao atualizar canais de status:', error);
        }
    }, 5 * 60 * 1000); // A cada 5 minutos

    // Verificar configurações essenciais
    checkConfiguration();

    // Criar API key padrão se não existir (apenas se banco estiver disponível)
    if (database.isDatabaseAvailable()) {
        await createDefaultApiKey();
    } else {
        console.log('⚠️ Banco de dados não disponível - pulando criação de API key');
    }

    // Keep-alive para Render (evitar sleep)
    if (process.env.RENDER_EXTERNAL_URL) {
        setInterval(() => {
            console.log(`💓 Keep-alive: ${new Date().toISOString()}`);
        }, 5 * 60 * 1000); // A cada 5 minutos
    }
});

// Criar API key padrão APENAS se não existir nenhuma
async function createDefaultApiKey() {
    try {
        // Verificar se já existe alguma API key
        const existingKeys = await database.getAllApiKeys();

        if (existingKeys && existingKeys.length > 0) {
            console.log('🔑 API Keys existentes encontradas - não criando nova');
            console.log(`   Total de keys: ${existingKeys.length}`);
            return;
        }

        const crypto = require('crypto');
        const Utils = require('./utils');

        const defaultApiKey = 'sp_' + crypto.randomBytes(32).toString('hex');
        const keyId = Utils.generateId();

        await database.createApiKey({
            id: keyId,
            key_name: 'Default API Key',
            api_key: defaultApiKey,
            permissions: 'admin,validate',
            expires_at: null
        });

        console.log('🔑 API Key padrão criada (primeira vez):');
        console.log(`   ID: ${keyId}`);
        console.log(`   Key: ${defaultApiKey}`);
        console.log('   Permissões: admin,validate');
        console.log('   ⚠️  Guarde esta chave em local seguro!');

    } catch (error) {
        console.error('Erro ao verificar/criar API key padrão:', error);
    }
}

// Verificar configurações essenciais
function checkConfiguration() {
    const essentialVars = [
        'DISCORD_TOKEN',
        'DISCORD_CLIENT_ID'
    ];

    const optionalVars = [
        'DISCORD_GUILD_ID',
        'ADMIN_ROLE_ID',
        'MERCADO_PAGO_ACCESS_TOKEN'
    ];

    const missingEssential = essentialVars.filter(varName => !process.env[varName]);
    const missingOptional = optionalVars.filter(varName => !process.env[varName]);

    if (missingEssential.length > 0) {
        console.error('❌ Variáveis essenciais faltando:', missingEssential.join(', '));
    }

    if (missingOptional.length > 0) {
        console.warn('⚠️ Variáveis opcionais faltando:', missingOptional.join(', '));
    }

    if (!process.env.WEBHOOK_URL) {
        console.warn('⚠️ WEBHOOK_URL não configurada - notificações automáticas de pagamento desabilitadas');
    }

    if (!process.env.CATEGORY_VENDAS_ID) {
        console.warn('⚠️ CATEGORY_VENDAS_ID não configurada - canais de compra serão criados na raiz');
    }

    // Log do ambiente
    console.log(`🌍 Ambiente: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🖥️ Plataforma: ${process.platform}`);
    console.log(`📦 Node.js: ${process.version}`);
}

// Função segura para responder interações (anti-crash)
async function safeReply(interaction, message) {
    try {
        const errorEmbed = {
            embeds: [{
                title: '❌ Erro',
                description: message,
                color: 0xDC3545,
                timestamp: new Date().toISOString()
            }],
            flags: 64 // Ephemeral
        };

        if (interaction.replied) {
            await interaction.followUp(errorEmbed);
        } else if (interaction.deferred) {
            await interaction.editReply(errorEmbed);
        } else {
            await interaction.reply(errorEmbed);
        }
    } catch (replyError) {
        console.error('❌ ANTI-CRASH: Erro ao responder interação:', replyError);
        // Não fazer nada - evitar crash
    }
}

// Registrar comandos slash
async function registerSlashCommands() {
    const commands = [
        configurarCommand.data.toJSON(),
        aprovarCommand.data.toJSON()
    ];

    const rest = new REST({ version: '10' }).setToken(process.env.DISCORD_TOKEN);

    try {
        console.log('🔄 Registrando comandos slash...');

        // Registrar globalmente se GUILD_ID não estiver configurado
        if (process.env.DISCORD_GUILD_ID) {
            await rest.put(
                Routes.applicationGuildCommands(process.env.DISCORD_CLIENT_ID, process.env.DISCORD_GUILD_ID),
                { body: commands }
            );
            console.log('✅ Comandos slash registrados para servidor específico!');
        } else {
            await rest.put(
                Routes.applicationCommands(process.env.DISCORD_CLIENT_ID),
                { body: commands }
            );
            console.log('✅ Comandos slash registrados globalmente!');
            console.log('⚠️ Comandos globais podem levar até 1 hora para aparecer');
        }
    } catch (error) {
        console.error('❌ Erro ao registrar comandos:', error);
        console.error('Detalhes:', error.message);
    }
}

// Evento: Interações (comandos e botões)
client.on('interactionCreate', async (interaction) => {
    try {
        // Check if interaction is too old (mais tolerante - 15 minutos)
        const interactionAge = Date.now() - interaction.createdTimestamp;
        const maxAge = 15 * 60 * 1000; // 15 minutes in milliseconds

        if (interactionAge > maxAge) {
            console.warn(`⚠️ Interação muito antiga (${Math.round(interactionAge / 1000)}s) - ignorando`);
            return;
        }

        // Verificar se a interação ainda é válida
        if (interaction.isRepliable() && (interaction.replied || interaction.deferred)) {
            console.warn(`⚠️ Interação já foi respondida - ignorando`);
            return;
        }

        if (interaction.isChatInputCommand()) {
            // Comandos slash
            // Log apenas em desenvolvimento
            if (process.env.NODE_ENV !== 'production') {
                console.log(`🎯 Comando: ${interaction.commandName} por ${interaction.user.tag}`);
            }

            const command = client.commands.get(interaction.commandName);
            if (command) {
                await command.execute(interaction);
            } else {
                console.error(`❌ Comando não encontrado: ${interaction.commandName}`);
            }
        } else if (interaction.isButton()) {
            // Botões - com proteção anti-crash
            try {
                await handleButtonInteraction(interaction);
            } catch (buttonError) {
                console.error('❌ ANTI-CRASH: Erro em botão:', buttonError);
                await safeReply(interaction, 'Erro ao processar botão. Tente novamente.');
            }
        } else if (interaction.isStringSelectMenu()) {
            // Select Menus - com proteção anti-crash
            try {
                await handleSelectMenuInteraction(interaction);
            } catch (selectError) {
                console.error('❌ ANTI-CRASH: Erro em select menu:', selectError);
                await safeReply(interaction, 'Erro ao processar seleção. Tente novamente.');
            }
        } else if (interaction.isModalSubmit()) {
            // Modais - com proteção anti-crash
            try {
                await handleModalInteraction(interaction);
            } catch (modalError) {
                console.error('❌ ANTI-CRASH: Erro em modal:', modalError);
                await safeReply(interaction, 'Erro ao processar formulário. Tente novamente.');
            }
        }
    } catch (error) {
        console.error('Erro ao processar interação:', error);

        // Check if the error is related to unknown interaction or expired interaction
        if (error.code === 10062) {
            console.warn('⚠️ Interação expirada ou desconhecida - ignorando resposta');
            return;
        }

        if (error.code === 40060) {
            console.warn('⚠️ Interação já foi respondida - ignorando resposta duplicada');
            return;
        }

        const errorMessage = {
            embeds: [{
                title: '❌ Erro Interno',
                description: 'Ocorreu um erro interno. Tente novamente em alguns instantes.',
                color: 0xDC3545,
                timestamp: new Date().toISOString()
            }],
            flags: 64 // Ephemeral flag
        };

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        } catch (responseError) {
            console.error('Erro ao responder com mensagem de erro:', responseError);
            // If we can't respond, just log and continue
        }
    }
});

// Manipular interações de botões
async function handleButtonInteraction(interaction) {
    const customId = interaction.customId;

    // Botões de configuração de produtos e canais de status
    if (['create_product', 'list_products', 'view_stats', 'edit_product', 'delete_product', 'publish_product',
        'create_member_counter', 'create_api_status', 'manage_status_channels',
        'update_status_channels', 'delete_all_status_channels'].includes(customId) ||
        customId.startsWith('edit_product_') || customId.startsWith('delete_product_') || customId.startsWith('publish_product_') ||
        customId.startsWith('confirm_delete_') || customId === 'cancel_delete') {
        await configurarCommand.handleButton(interaction, database);
    }

    // Botões do comando aprovar
    else if (customId.startsWith('confirm_approve_') || customId === 'cancel_approve') {
        await aprovarCommand.handleButton(interaction, database);
    }



    // Botões de compra
    else if (customId.startsWith('buy_product_')) {
        await purchaseHandler.handlePurchaseButton(interaction);
    }

    // Botões de termos e pagamento
    else if (customId.startsWith('accept_terms_')) {
        console.log(`🔧 Processando aceitação de termos: ${customId}`);
        console.log(`👤 Usuário: ${interaction.user.tag}`);
        console.log(`📍 Canal: ${interaction.channel.name}`);
        try {
            await purchaseHandler.handleTermsAcceptance(interaction);
            console.log(`✅ Aceitação de termos processada com sucesso`);
        } catch (error) {
            console.error(`❌ ERRO na aceitação de termos:`, error);
            console.error(`Stack trace:`, error.stack);
            throw error;
        }
    }
    else if (customId.startsWith('cancel_purchase_') || customId.startsWith('cancel_payment_')) {
        console.log(`🔧 Processando cancelamento: ${customId}`);
        await purchaseHandler.handleCancelPurchase(interaction);
    }
    else if (customId.startsWith('check_payment_')) {
        await purchaseHandler.handleManualPaymentCheck(interaction);
    }
    else if (customId === 'close_channel') {
        await purchaseHandler.handleCloseChannel(interaction);
    }

    // Outros botões
    else if (customId === 'open_support_ticket') {
        await handleSupportTicket(interaction);
    }
}

// Manipular interações de select menus
async function handleSelectMenuInteraction(interaction) {
    console.log(`🔧 Select menu interaction: ${interaction.customId}`);

    // Select menus do comando configurar
    if (interaction.customId === 'main_config_select' ||
        interaction.customId.startsWith('select_product_') ||
        interaction.customId.startsWith('select_channel_') ||
        interaction.customId.startsWith('edit_option_') ||
        interaction.customId.startsWith('product_settings_') ||
        interaction.customId.startsWith('changelog_channel_') ||
        interaction.customId.startsWith('announcement_role_') ||
        interaction.customId === 'select_category_members' ||
        interaction.customId === 'select_category_api') {
        console.log(`✅ Processando select menu: ${interaction.customId}`);
        await configurarCommand.handleSelectMenu(interaction, database);
    } else {
        console.log(`❌ Select menu não reconhecido: ${interaction.customId}`);
    }
}

// Manipular interações de modais
async function handleModalInteraction(interaction) {
    if (interaction.customId === 'create_product_modal') {
        await configurarCommand.handleCreateProductModal(interaction, database);
    } else if (interaction.customId.startsWith('edit_product_modal_')) {
        await configurarCommand.handleEditProductModal(interaction, database);
    } else if (interaction.customId.startsWith('update_product_modal_')) {
        await configurarCommand.handleUpdateProductModal(interaction, database);
    } else if (interaction.customId.startsWith('basic_info_modal_')) {
        await configurarCommand.handleBasicInfoModal(interaction, database);
    } else if (interaction.customId.startsWith('download_link_modal_')) {
        await configurarCommand.handleDownloadLinkModal(interaction, database);
    }
}

// Manipular tickets de suporte
async function handleSupportTicket(interaction) {
    const embed = {
        title: '🎧 Suporte Técnico',
        description: 'Para suporte técnico, entre em contato conosco:',
        fields: [
            { name: '📧 Email', value: '<EMAIL>', inline: true },
            { name: '💬 Discord', value: '[Servidor de Suporte](https://discord.gg/stoneplugins)', inline: true },
            { name: '📚 Documentação', value: '[docs.stoneplugins.com](https://docs.stoneplugins.com)', inline: true }
        ],
        color: 0x0DCAF0,
        timestamp: new Date().toISOString()
    };

    await interaction.reply({
        embeds: [embed],
        flags: 64 // Ephemeral flag
    });
}

// Evento: Mensagens (para comandos de texto se necessário)
client.on('messageCreate', async (message) => {
    if (message.author.bot) return;

    // Aqui você pode adicionar comandos de texto se necessário
});

// Função para atualizar canais de status
async function updateStatusChannels(client) {
    try {
        const StatusChannelsManager = require('./statusChannels');
        const statusManager = new StatusChannelsManager();
        const statusChannels = statusManager.getChannels();

        if (Object.keys(statusChannels).length === 0) {
            return; // Nenhum canal de status configurado
        }

        const guild = client.guilds.cache.first(); // Assumindo que o bot está em apenas um servidor
        if (!guild) return;

        const { SupabaseLicenseManager } = require('./supabase');
        const supabaseLicenseManager = new SupabaseLicenseManager();

        // Atualizar contador de membros
        if (statusChannels.memberCounter) {
            const channel = guild.channels.cache.get(statusChannels.memberCounter);
            if (channel) {
                try {
                    const memberCount = guild.memberCount;
                    const newName = `Membros no Discord: ${memberCount}`;
                    if (channel.name !== newName) {
                        await channel.setName(newName);
                        console.log(`📊 Canal de membros atualizado: ${newName}`);
                    }
                } catch (error) {
                    console.error('Erro ao atualizar contador de membros:', error);
                }
            }
        }

        // Atualizar status da API
        if (statusChannels.apiStatus) {
            const channel = guild.channels.cache.get(statusChannels.apiStatus);
            if (channel) {
                try {
                    // Por enquanto, sempre mostrar como online se o bot está rodando
                    const apiStatus = '🟢';

                    const newName = `Licenças/API Status: ${apiStatus}`;
                    if (channel.name !== newName) {
                        await channel.setName(newName);
                        console.log(`🔗 Canal de API atualizado: ${newName}`);
                    }
                } catch (error) {
                    console.error('Erro ao atualizar status da API:', error);
                }
            }
        }



    } catch (error) {
        console.error('Erro geral ao atualizar canais de status:', error);
    }
}

// Evento: Erro
client.on('error', (error) => {
    console.error('❌ Erro do Discord:', error);
});

// Evento: Aviso
client.on('warn', (warning) => {
    console.warn('⚠️ Aviso do Discord:', warning);
});

// SISTEMA ANTI-CRASH - Manipulação robusta de erros
process.on('unhandledRejection', (error, promise) => {
    console.error('❌ ANTI-CRASH: Erro não tratado capturado:', error);
    console.error('Promise rejeitada:', promise);
    // NÃO sair - apenas logar o erro
});

process.on('uncaughtException', (error) => {
    console.error('❌ ANTI-CRASH: Exceção não capturada:', error);
    console.error('Stack trace:', error.stack);
    // NÃO sair - apenas logar o erro
    console.log('🔄 ANTI-CRASH: Continuando execução...');
});

// Capturar erros do cliente Discord
client.on('error', (error) => {
    console.error('❌ ANTI-CRASH: Erro do Discord Client:', error);
    console.log('🔄 ANTI-CRASH: Cliente Discord continua funcionando...');
});

client.on('warn', (warning) => {
    console.warn('⚠️ ANTI-CRASH: Aviso do Discord:', warning);
});

// Reconexão automática se desconectar
client.on('disconnect', () => {
    console.warn('⚠️ ANTI-CRASH: Bot desconectado, tentando reconectar...');
    setTimeout(() => {
        client.login(process.env.DISCORD_TOKEN).catch(error => {
            console.error('❌ ANTI-CRASH: Erro na reconexão:', error);
            setTimeout(() => client.login(process.env.DISCORD_TOKEN), 5000);
        });
    }, 2000);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM recebido, desligando graciosamente...');
    licenseAPI.stop();
    database.close();
    client.destroy();
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT recebido, desligando graciosamente...');
    licenseAPI.stop();
    database.close();
    client.destroy();
    process.exit(0);
});

// Timeout para detectar problemas de conexão (aumentado para 60s)
const loginTimeout = setTimeout(() => {
    console.error('❌ Timeout: Bot não conseguiu se conectar em 60 segundos');
    console.error('Tentando reconectar...');
    // Não sair, apenas tentar novamente
    client.login(process.env.DISCORD_TOKEN).catch(console.error);
}, 60000);

// Iniciar bot
console.log('🔄 Conectando ao Discord...');
client.login(process.env.DISCORD_TOKEN).catch(error => {
    clearTimeout(loginTimeout);
    console.error('❌ Erro ao conectar:', error);
    process.exit(1);
});

// Limpar timeout quando bot conectar
client.once('ready', () => {
    clearTimeout(loginTimeout);
});