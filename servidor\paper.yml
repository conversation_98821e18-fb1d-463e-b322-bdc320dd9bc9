# This is the main configuration file for PaperSpigot.
# As you can see, there's tons to configure. Some options may impact gameplay, so use
# with caution, and make sure you know what each option does before configuring.
# 
# If you need help with the configuration or have any questions related to PaperSpigot,
# join us at the IRC.
# 
# IRC: #paperspigot @ irc.spi.gt ( http://irc.spi.gt/iris/?channels=PaperSpigot )

config-version: 9
stackable-buckets:
  lava: false
  water: false
  milk: false
effect-modifiers:
  strength: 1.3
  weakness: -0.5
data-value-allowed-items: []
warnWhenSettingExcessiveVelocity: true
settings:
  baby-zombie-movement-speed: 0.5
  limit-player-interactions: true
world-settings:
  default:
    verbose: true
    disable-explosion-knockback: false
    fix-cannons: false
    water-over-lava-flow-speed: 5
    tnt-entity-height-nerf: 0
    use-async-lighting: false
    disable-thunder: false
    tick-next-tick-list-cap: 10000
    tick-next-tick-list-cap-ignores-redstone: false
    all-chunks-are-slime-chunks: false
    allow-undead-horse-leashing: false
    keep-spawn-loaded: true
    game-mechanics:
      disable-end-credits: false
      boats-drop-boats: false
      disable-player-crits: false
      disable-chest-cat-detection: false
    container-update-tick-rate: 1
    falling-blocks-collide-with-signs: false
    disable-ice-and-snow: false
    portal-search-radius: 128
    disable-mood-sounds: false
    allow-block-location-tab-completion: true
    generator-settings:
      canyon: true
      caves: true
      dungeon: true
      fortress: true
      mineshaft: true
      monument: true
      stronghold: true
      temple: true
      village: true
      flat-bedrock: false
    use-hopper-check: false
    player-blocking-damage-multiplier: 0.5
    mob-spawner-tick-rate: 1
    optimize-explosions: false
    max-growth-height:
      cactus: 3
      reeds: 3
    tnt-explosion-volume: 4.0
    falling-block-height-nerf: 0
    nether-ceiling-void-damage: false
    squid-spawn-height:
      minimum: 45.0
      maximum: 63.0
    fishing-time-range:
      MinimumTicks: 100
      MaximumTicks: 900
    load-chunks:
      enderpearls: false
      tnt-entities: false
      falling-blocks: false
    player-exhaustion:
      block-break: 0.02500000037252903
      swimming: 0.014999999664723873
    fast-drain:
      lava: false
      water: false
    remove-unloaded:
      enderpearls: true
      tnt-entities: true
      falling-blocks: true
    lava-flow-speed:
      normal: 30
      nether: 10
    despawn-ranges:
      soft: 32
      hard: 128
    disable-teleportation-suffocation-check: false
    cache-chunk-maps: false
    remove-invalid-mob-spawner-tile-entities: true
