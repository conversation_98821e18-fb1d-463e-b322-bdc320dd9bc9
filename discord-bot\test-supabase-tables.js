const { SupabaseLicenseManager } = require('./supabase');

async function testSupabaseTables() {
    try {
        console.log('🧪 Testando tabelas do Supabase...');
        
        const supabaseLicenseManager = new SupabaseLicenseManager();
        
        // Teste 1: Verificar se product_configs existe e funciona
        console.log('📋 Teste 1: Verificando product_configs...');
        try {
            const { data, error } = await supabaseLicenseManager.supabase
                .from('product_configs')
                .select('*')
                .limit(1);
                
            if (error) {
                console.error('❌ Erro em product_configs:', error);
            } else {
                console.log('✅ Tabela product_configs OK');
            }
        } catch (error) {
            console.error('❌ Erro ao testar product_configs:', error.message);
        }
        
        // Teste 2: Verificar se published_messages existe e funciona
        console.log('📋 Teste 2: Verificando published_messages...');
        try {
            const { data, error } = await supabaseLicenseManager.supabase
                .from('published_messages')
                .select('*')
                .limit(1);
                
            if (error) {
                console.error('❌ Erro em published_messages:', error);
                if (error.code === '42P01') {
                    console.log('💡 SOLUÇÃO: Execute este SQL no Supabase:');
                    console.log(`
CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
                    `);
                }
            } else {
                console.log('✅ Tabela published_messages OK');
            }
        } catch (error) {
            console.error('❌ Erro ao testar published_messages:', error.message);
        }
        
        // Teste 3: Testar inserção em product_configs
        console.log('📋 Teste 3: Testando inserção em product_configs...');
        try {
            const testConfig = {
                product_id: 'test_product_' + Date.now(),
                config_key: 'test_key',
                config_value: 'test_value'
            };
            
            const { data, error } = await supabaseLicenseManager.supabase
                .from('product_configs')
                .insert(testConfig)
                .select();
                
            if (error) {
                console.error('❌ Erro ao inserir em product_configs:', error);
            } else {
                console.log('✅ Inserção em product_configs OK');
                
                // Remover teste
                await supabaseLicenseManager.supabase
                    .from('product_configs')
                    .delete()
                    .eq('product_id', testConfig.product_id);
                    
                console.log('✅ Teste removido de product_configs');
            }
        } catch (error) {
            console.error('❌ Erro no teste de inserção product_configs:', error.message);
        }
        
        // Teste 4: Testar inserção em published_messages (se existir)
        console.log('📋 Teste 4: Testando inserção em published_messages...');
        try {
            const testMessage = {
                message_id: 'test_msg_' + Date.now(),
                channel_id: 'test_channel',
                product_id: 'test_product',
                guild_id: 'test_guild'
            };
            
            const { data, error } = await supabaseLicenseManager.supabase
                .from('published_messages')
                .insert(testMessage)
                .select();
                
            if (error) {
                console.error('❌ Erro ao inserir em published_messages:', error);
            } else {
                console.log('✅ Inserção em published_messages OK');
                
                // Remover teste
                await supabaseLicenseManager.supabase
                    .from('published_messages')
                    .delete()
                    .eq('message_id', testMessage.message_id);
                    
                console.log('✅ Teste removido de published_messages');
            }
        } catch (error) {
            console.error('❌ Erro no teste de inserção published_messages:', error.message);
        }
        
        // Teste 5: Testar funções do sistema
        console.log('📋 Teste 5: Testando funções do sistema...');
        try {
            // Testar saveProductConfig
            await supabaseLicenseManager.saveProductConfig('test_product', 'test_key', 'test_value');
            console.log('✅ saveProductConfig funcionando');
            
            // Testar getProductConfig
            const config = await supabaseLicenseManager.getProductConfig('test_product');
            console.log('✅ getProductConfig funcionando:', config);
            
            // Limpar teste
            await supabaseLicenseManager.supabase
                .from('product_configs')
                .delete()
                .eq('product_id', 'test_product');
                
        } catch (error) {
            console.error('❌ Erro no teste de funções:', error.message);
        }
        
        console.log('🎉 Teste concluído!');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    testSupabaseTables()
        .then(() => {
            console.log('✅ Teste finalizado');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Erro no teste:', error);
            process.exit(1);
        });
}

module.exports = { testSupabaseTables };