@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Compilando StonePayments com Maven
echo    (Versao com Sistema de Licencas)
echo ========================================

REM Usar o Maven que existe no codebase
set "MAVEN_CMD=..\apache-maven-3.9.5\bin\mvn.cmd"

if not exist "%MAVEN_CMD%" (
    echo ERRO: Maven nao encontrado em %MAVEN_CMD%
    echo Tentando usar Maven do sistema...
    where mvn >nul 2>&1
    if !errorlevel! neq 0 (
        echo ERRO: Maven nao encontrado no PATH!
        echo Instale o Maven ou coloque apache-maven-3.9.5 na pasta pai
        pause
        exit /b 1
    )
    set "MAVEN_CMD=mvn"
)

echo Usando Maven: %MAVEN_CMD%
echo.

REM Compilar com Maven
echo Executando: %MAVEN_CMD% clean package
"%MAVEN_CMD%" clean package

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao Maven!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    COMPILACAO CONCLUIDA!
echo ========================================

REM Verificar se o JAR foi gerado
if not exist "target\stonepayments-1.0.0.jar" (
    echo ERRO: Arquivo JAR nao foi gerado!
    pause
    exit /b 1
)

echo Arquivo gerado: target\stonepayments-1.0.0.jar
for %%I in (target\stonepayments-1.0.0.jar) do echo Tamanho: %%~zI bytes
echo.

echo Copiando para o servidor...

REM Copiar para o servidor
set "SERVER_PLUGINS=..\servidor\plugins"
set "TARGET_FILE=%SERVER_PLUGINS%\stonepayments-1.0.0.jar"

REM Criar pasta se nao existir
if not exist "%SERVER_PLUGINS%" (
    mkdir "%SERVER_PLUGINS%"
    echo Pasta plugins criada: %SERVER_PLUGINS%
)

REM Fazer backup se existir
if exist "%TARGET_FILE%" (
    set "BACKUP_FILE=%SERVER_PLUGINS%\stonepayments-1.0.0-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.jar"
    set "BACKUP_FILE=!BACKUP_FILE: =0!"
    copy "%TARGET_FILE%" "!BACKUP_FILE!" >nul
    echo Backup criado: !BACKUP_FILE!
)

REM Remover versoes antigas do StonePayments
echo Removendo versoes antigas...
del "%SERVER_PLUGINS%\stonepayments-*.jar" 2>nul

REM Copiar novo plugin
copy "target\stonepayments-1.0.0.jar" "%TARGET_FILE%" >nul

if exist "%TARGET_FILE%" (
    echo Plugin copiado para o servidor com sucesso!
    echo Localizacao: %TARGET_FILE%
    echo.
    echo ========================================
    echo    STONEPAYMENTS INSTALADO!
    echo ========================================
    echo.
    echo PROXIMOS PASSOS:
    echo 1. INSTALE StonePluginsDepend (OBRIGATORIO)
    echo 2. Configure o token do Mercado Pago em config.yml
    echo 3. Personalize os produtos em products.yml
    echo 4. Reinicie o servidor
    echo 5. Ative a licenca com /stoneplugins ativar ^<licenca^>
    echo 6. Use /comprar para abrir a loja
    echo.
    echo IMPORTANTE:
    echo - StonePluginsDepend eh OBRIGATORIO para funcionar
    echo - Configure o access_token do Mercado Pago
    echo - Ative a licenca para processar pagamentos
    echo - Plugin funciona em modo limitado sem licenca
    echo - Teste primeiro com valores baixos
    echo - Verifique os logs para debug
    echo.
    echo COMANDOS DISPONIVEIS:
    echo - /comprar (abrir loja de pagamentos)
    echo - /stonepayments info (informacoes do plugin)
    echo - /stonepayments license (status da licenca)
    echo - /stoneplugins ativar ^<licenca^> (ativar licenca)
    echo - /stoneplugins status (verificar sistema)
    echo.
    echo LICENCIAMENTO:
    echo - Licenca ativa: Todas as funcionalidades
    echo - Sem licenca: Modo limitado (apenas visualizacao)
    echo - Uma licenca funciona apenas em um servidor
    echo - Verificacao automatica a cada 30 minutos
    echo.
) else (
    echo ERRO: Falha ao copiar plugin para o servidor.
    echo Plugin disponivel em: target\stonepayments-1.0.0.jar
)

pause