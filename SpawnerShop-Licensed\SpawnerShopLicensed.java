package com.stoneplugins.spawnershop;

import com.stoneplugins.core.StonePluginInterface;
import com.stoneplugins.core.StonePluginsCore;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * SpawnerShop com sistema de licenças integrado
 * Requer StonePlugins-Core para funcionar
 */
public class SpawnerShopLicensed extends JavaPlugin implements Listener, StonePluginInterface {
    
    // Dados do plugin original
    private Map<String, SpawnerInfo> spawners = new HashMap<>();
    private Map<UUID, PlayerData> playerData = new HashMap<>();
    private Map<UUID, String> playerChat = new HashMap<>();
    private Connection connection;
    private String dbPath;
    private int maxMultiplicador = 100;
    private Map<String, Integer> topCompradores = new LinkedHashMap<>();
    private Map<String, Integer> topLimites = new LinkedHashMap<>();
    
    // Sistema de licenças
    private boolean licenseActive = false;
    private boolean corePluginAvailable = false;
    
    @Override
    public void onEnable() {
        // Verificar se StonePlugins-Core está disponível
        if (!checkCorePlugin()) {
            getLogger().severe("§c[SpawnerShop] StonePlugins-Core não encontrado!");
            getLogger().severe("§c[SpawnerShop] Este plugin requer StonePlugins-Core para funcionar.");
            getLogger().severe("§c[SpawnerShop] Download: https://stoneplugins.com/core");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Registrar plugin no sistema de licenças
        StonePluginsCore.registerPlugin(getName(), getDescription().getVersion(), getRequiredLicenseName());
        
        // Verificar licença
        if (!checkLicense()) {
            getLogger().warning("§c[SpawnerShop] Licença não encontrada!");
            getLogger().warning("§c[SpawnerShop] Use /stoneplugins ativar <licença> para ativar.");
            getLogger().warning("§c[SpawnerShop] Plugin funcionará em modo limitado.");
        }
        
        // Salvar config padrão se não existir
        saveDefaultConfig();
        
        // Registrar eventos
        getServer().getPluginManager().registerEvents(this, this);
        
        // Inicializar banco de dados
        initDatabase();
        
        // Carregar configurações
        loadConfig();
        
        // Inicializar sistema
        if (licenseActive) {
            initializeFullSystem();
        } else {
            initializeLimitedSystem();
        }
        
        getLogger().info("§a[SpawnerShop] Plugin inicializado!");
        if (licenseActive) {
            getLogger().info("§a[SpawnerShop] Licença ativa - Todas as funcionalidades disponíveis!");
        } else {
            getLogger().warning("§e[SpawnerShop] Modo limitado - Algumas funcionalidades restritas!");
        }
    }
    
    @Override
    public void onDisable() {
        // Fechar conexão com banco
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        
        getLogger().info("§c[SpawnerShop] Plugin desabilitado!");
    }
    
    /**
     * Verificar se StonePlugins-Core está disponível
     */
    private boolean checkCorePlugin() {
        org.bukkit.plugin.Plugin corePlugin = getServer().getPluginManager().getPlugin("StonePlugins-Core");
        corePluginAvailable = corePlugin != null && corePlugin.isEnabled();
        return corePluginAvailable;
    }
    
    /**
     * Verificar licença
     */
    private boolean checkLicense() {
        if (!corePluginAvailable) {
            return false;
        }
        
        licenseActive = StonePluginsCore.hasActiveLicense(getRequiredLicenseName());
        return licenseActive;
    }
    
    /**
     * Inicializar sistema completo (com licença)
     */
    private void initializeFullSystem() {
        getLogger().info("§a[SpawnerShop] Inicializando sistema completo...");
        
        // Carregar todos os spawners
        loadSpawners();
        
        // Iniciar tarefas automáticas
        startAutomaticTasks();
        
        // Habilitar todas as funcionalidades
        enableAllFeatures();
    }
    
    /**
     * Inicializar sistema limitado (sem licença)
     */
    private void initializeLimitedSystem() {
        getLogger().warning("§e[SpawnerShop] Inicializando sistema limitado...");
        
        // Carregar apenas spawners básicos
        loadBasicSpawners();
        
        // Mostrar mensagem de limitação
        showLimitationMessage();
    }
    
    /**
     * Carregar spawners básicos (modo limitado)
     */
    private void loadBasicSpawners() {
        // Apenas 3 tipos de spawners no modo limitado
        spawners.put("PIG", new SpawnerInfo("PIG", "Porco", 100.0, Material.PORK));
        spawners.put("COW", new SpawnerInfo("COW", "Vaca", 150.0, Material.BEEF));
        spawners.put("CHICKEN", new SpawnerInfo("CHICKEN", "Galinha", 80.0, Material.CHICKEN));
        
        getLogger().info("§e[SpawnerShop] Carregados 3 spawners (modo limitado)");
    }
    
    /**
     * Carregar todos os spawners (modo completo)
     */
    private void loadSpawners() {
        // Carregar todos os spawners da configuração
        // ... código original do plugin ...
        
        getLogger().info("§a[SpawnerShop] Carregados todos os spawners (modo completo)");
    }
    
    /**
     * Mostrar mensagem de limitação
     */
    private void showLimitationMessage() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("spawnershop.admin")) {
                        player.sendMessage("§c§l[SpawnerShop] §cPlugin em modo limitado!");
                        player.sendMessage("§e[SpawnerShop] Ative a licença para acessar todas as funcionalidades.");
                        player.sendMessage("§e[SpawnerShop] Use: §f/stoneplugins ativar <licença>");
                    }
                }
            }
        }.runTaskLater(this, 20L * 5L); // 5 segundos após iniciar
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!command.getName().equalsIgnoreCase("spawnershop")) {
            return false;
        }
        
        // Verificar licença para comandos avançados
        if (args.length > 0 && !licenseActive) {
            String subCommand = args[0].toLowerCase();
            if (isAdvancedCommand(subCommand)) {
                sender.sendMessage("§c[SpawnerShop] Este comando requer licença ativa!");
                sender.sendMessage("§e[SpawnerShop] Use: §f/stoneplugins ativar <licença>");
                return true;
            }
        }
        
        // Processar comandos normalmente
        return processCommand(sender, args);
    }
    
    /**
     * Verificar se é um comando avançado (requer licença)
     */
    private boolean isAdvancedCommand(String command) {
        return Arrays.asList("admin", "reload", "stats", "top", "multiplicador").contains(command);
    }
    
    /**
     * Processar comandos
     */
    private boolean processCommand(CommandSender sender, String[] args) {
        if (args.length == 0) {
            if (sender instanceof Player) {
                openShopGUI((Player) sender);
            } else {
                sender.sendMessage("§c[SpawnerShop] Apenas jogadores podem usar este comando!");
            }
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "shop":
                if (sender instanceof Player) {
                    openShopGUI((Player) sender);
                }
                break;
                
            case "info":
                showPluginInfo(sender);
                break;
                
            case "license":
                showLicenseInfo(sender);
                break;
                
            // Comandos avançados (já verificados acima)
            case "admin":
                if (licenseActive) {
                    handleAdminCommand(sender, args);
                }
                break;
                
            default:
                sender.sendMessage("§c[SpawnerShop] Comando inválido!");
                break;
        }
        
        return true;
    }
    
    /**
     * Mostrar informações do plugin
     */
    private void showPluginInfo(CommandSender sender) {
        sender.sendMessage("§6§l[SpawnerShop] §fInformações:");
        sender.sendMessage("§7Versão: §f" + getDescription().getVersion());
        sender.sendMessage("§7Status: " + (licenseActive ? "§aLicenciado" : "§eModo Limitado"));
        sender.sendMessage("§7Spawners: §f" + spawners.size());
        if (!licenseActive) {
            sender.sendMessage("§e[SpawnerShop] Para acessar todas as funcionalidades:");
            sender.sendMessage("§e[SpawnerShop] Use: §f/stoneplugins ativar <licença>");
        }
    }
    
    /**
     * Mostrar informações da licença
     */
    private void showLicenseInfo(CommandSender sender) {
        sender.sendMessage("§6§l[SpawnerShop] §fStatus da Licença:");
        if (licenseActive) {
            sender.sendMessage("§a✅ Licença ativa - Todas as funcionalidades disponíveis!");
        } else {
            sender.sendMessage("§c❌ Licença não encontrada!");
            sender.sendMessage("§e[SpawnerShop] Para ativar sua licença:");
            sender.sendMessage("§e[SpawnerShop] 1. Use: §f/stoneplugins ativar <sua-licença>");
            sender.sendMessage("§e[SpawnerShop] 2. Reinicie o plugin ou use: §f/spawnershop reload");
        }
    }
    
    /**
     * Abrir GUI da loja
     */
    private void openShopGUI(Player player) {
        if (!licenseActive && spawners.size() > 3) {
            player.sendMessage("§e[SpawnerShop] Modo limitado - Apenas 3 spawners disponíveis!");
        }
        
        // Criar GUI com spawners disponíveis
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lSpawner Shop" + 
                                             (licenseActive ? "" : " §c(Limitado)"));
        
        int slot = 0;
        for (SpawnerInfo spawner : spawners.values()) {
            if (slot >= 54) break;
            
            ItemStack item = createSpawnerItem(spawner);
            gui.setItem(slot, item);
            slot++;
        }
        
        player.openInventory(gui);
    }
    
    /**
     * Criar item do spawner para GUI
     */
    private ItemStack createSpawnerItem(SpawnerInfo spawner) {
        ItemStack item = new ItemStack(Material.SPAWNER);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§6" + spawner.getDisplayName());
        
        List<String> lore = new ArrayList<>();
        lore.add("§7Preço: §a$" + spawner.getPrice());
        lore.add("§7Tipo: §f" + spawner.getType());
        lore.add("");
        lore.add("§eClique para comprar!");
        
        if (!licenseActive) {
            lore.add("");
            lore.add("§c⚠ Modo Limitado");
        }
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    // ==================== IMPLEMENTAÇÃO DA INTERFACE ====================
    
    @Override
    public void onLicenseActivated() {
        getLogger().info("§a[SpawnerShop] Licença ativada! Habilitando funcionalidades completas...");
        
        licenseActive = true;
        
        // Recarregar sistema completo
        new BukkitRunnable() {
            @Override
            public void run() {
                initializeFullSystem();
                
                // Notificar admins online
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("spawnershop.admin")) {
                        player.sendMessage("§a§l[SpawnerShop] §aLicença ativada com sucesso!");
                        player.sendMessage("§a[SpawnerShop] Todas as funcionalidades estão disponíveis!");
                    }
                }
            }
        }.runTask(this);
    }
    
    @Override
    public void onLicenseDeactivated() {
        getLogger().warning("§c[SpawnerShop] Licença desativada! Voltando ao modo limitado...");
        
        licenseActive = false;
        
        // Voltar ao modo limitado
        new BukkitRunnable() {
            @Override
            public void run() {
                initializeLimitedSystem();
                
                // Notificar admins online
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("spawnershop.admin")) {
                        player.sendMessage("§c§l[SpawnerShop] §cLicença desativada!");
                        player.sendMessage("§e[SpawnerShop] Plugin voltou ao modo limitado.");
                    }
                }
            }
        }.runTask(this);
    }
    
    @Override
    public String getRequiredLicenseName() {
        return "SpawnerShop"; // Nome do produto na licença
    }
    
    @Override
    public boolean canRunWithoutLicense() {
        return true; // Permite modo limitado
    }
    
    // ==================== CLASSES AUXILIARES ====================
    
    /**
     * Classe para armazenar informações do spawner
     */
    private static class SpawnerInfo {
        private String type;
        private String displayName;
        private double price;
        private Material icon;
        
        public SpawnerInfo(String type, String displayName, double price, Material icon) {
            this.type = type;
            this.displayName = displayName;
            this.price = price;
            this.icon = icon;
        }
        
        // Getters
        public String getType() { return type; }
        public String getDisplayName() { return displayName; }
        public double getPrice() { return price; }
        public Material getIcon() { return icon; }
    }
    
    /**
     * Classe para dados do jogador
     */
    private static class PlayerData {
        // Implementar conforme necessário
    }
    
    // ==================== MÉTODOS AUXILIARES ====================
    
    private void initDatabase() {
        // Implementar inicialização do banco de dados
    }
    
    private void loadConfig() {
        // Implementar carregamento de configuração
    }
    
    private void startAutomaticTasks() {
        // Implementar tarefas automáticas
    }
    
    private void enableAllFeatures() {
        // Implementar habilitação de todas as funcionalidades
    }
    
    private void handleAdminCommand(CommandSender sender, String[] args) {
        // Implementar comandos de admin
    }
}