-- <PERSON><PERSON><PERSON> completo para criar TODAS as tabelas necessárias no Supabase
-- Execute este SQL no painel do Supabase (SQL Editor)

-- 1. <PERSON><PERSON>r tabela para mensagens publicadas
CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON>riar tabela para configurações de produtos
CREATE TABLE IF NOT EXISTS product_configs (
    id SERIAL PRIMARY KEY,
    product_id TEXT NOT NULL,
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, config_key)
);

-- 3. <PERSON><PERSON><PERSON> índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_channel_id ON published_messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_guild_id ON published_messages(guild_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_message_id ON published_messages(message_id);

CREATE INDEX IF NOT EXISTS idx_product_configs_product_id ON product_configs(product_id);
CREATE INDEX IF NOT EXISTS idx_product_configs_key ON product_configs(config_key);

-- 4. Comentários para documentação
COMMENT ON TABLE published_messages IS 'Rastreia mensagens de produtos publicadas nos canais do Discord para permitir atualizações automáticas';
COMMENT ON COLUMN published_messages.message_id IS 'ID único da mensagem no Discord';
COMMENT ON COLUMN published_messages.channel_id IS 'ID do canal onde a mensagem foi publicada';
COMMENT ON COLUMN published_messages.product_id IS 'ID do produto que foi publicado';
COMMENT ON COLUMN published_messages.guild_id IS 'ID do servidor Discord';

COMMENT ON TABLE product_configs IS 'Configurações específicas de cada produto (canal de changelog, cargo de anúncios, etc.)';
COMMENT ON COLUMN product_configs.product_id IS 'ID do produto';
COMMENT ON COLUMN product_configs.config_key IS 'Chave da configuração (ex: changelog_channel_id, announcement_role_id)';
COMMENT ON COLUMN product_configs.config_value IS 'Valor da configuração';

-- 5. Verificar se as tabelas foram criadas corretamente
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('published_messages', 'product_configs')
ORDER BY tablename;

-- 6. Verificar estrutura das tabelas
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('published_messages', 'product_configs')
ORDER BY table_name, ordinal_position;

-- Mensagem de sucesso
SELECT 'SUCESSO: Todas as tabelas foram criadas!' as status;