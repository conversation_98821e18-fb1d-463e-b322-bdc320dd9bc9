const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ChannelType, PermissionFlagsBits, AttachmentBuilder } = require('discord.js');
const Utils = require('../utils');
const MercadoPagoService = require('../mercadopago');
const { SupabaseLicenseManager } = require('../supabase');
const fs = require('fs');
const path = require('path');

class PurchaseHandler {
    constructor(database) {
        this.database = database;
        this.mercadoPago = new MercadoPagoService();
        this.supabaseLicenseManager = new SupabaseLicenseManager();
        this.activePayments = new Map(); // Para monitorar pagamentos ativos
        this.activePurchases = new Map(); // Para evitar múltiplas compras simultâneas
    }

    async handlePurchaseButton(interaction) {
        const productId = interaction.customId.replace('buy_product_', '');
        const userId = interaction.user.id;

        try {
            // Verificar se a interação ainda é válida (não expirou)
            if (Date.now() - interaction.createdTimestamp > 15 * 60 * 1000) { // 15 minutos
                console.log('⚠️ Interação expirada - ignorando');
                return;
            }

            // Defer a resposta para evitar timeout
            await interaction.deferReply({ flags: 64 }); // Ephemeral flag

            // Verificar se o usuário já tem uma compra ativa
            if (this.activePurchases.has(userId)) {
                return interaction.editReply({
                    embeds: [Utils.createWarningEmbed('Você já tem uma compra em andamento! Finalize-a antes de iniciar outra.')]
                });
            }

            // Buscar produto (SQLite local ou Supabase)
            let product = null;

            if (this.database.isDatabaseAvailable()) {
                product = await this.database.getProduct(productId);
                console.log(`🔍 Produto recuperado do SQLite:`, {
                    id: product?.id,
                    title: product?.title,
                    download_link: product?.download_link
                });
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                try {
                    console.log(`🔍 Buscando produto no Supabase com ID: ${productId}`);
                    const result = await this.supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link || '',
                            price: parseFloat(result.price),
                            stock: 999 // Estoque infinito para produtos do Supabase
                        };
                        console.log(`📦 Produto encontrado no Supabase: ${product.title} - R$ ${product.price}`);
                        console.log(`📋 Dados do produto:`, JSON.stringify(product, null, 2));
                    } else {
                        console.log(`❌ Produto não encontrado no Supabase com ID: ${productId}`);
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                console.log(`❌ Produto final é null para ID: ${productId}`);
                return interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')]
                });
            }

            console.log(`✅ Produto final encontrado: ${product.title} (ID: ${product.id})`);
            console.log(`💰 Preço final: R$ ${product.price}`);

            // Verificar estoque
            if (product.stock === 0) {
                return interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Produto fora de estoque!')]
                });
            }

            // Marcar usuário como tendo compra ativa
            this.activePurchases.set(userId, { productId, timestamp: Date.now() });

            // Criar canal privado para a compra diretamente
            const channelName = Utils.generateChannelName(interaction.user.username, product.title);

            const channel = await interaction.guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                parent: process.env.CATEGORY_VENDAS_ID,
                permissionOverwrites: [
                    {
                        id: interaction.guild.id,
                        deny: [PermissionFlagsBits.ViewChannel],
                    },
                    {
                        id: interaction.user.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory
                        ],
                    },
                    {
                        id: interaction.client.user.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ManageChannels,
                            PermissionFlagsBits.ReadMessageHistory
                        ],
                    }
                ],
            });

            // Criar registro da venda (SQLite local ou Supabase)
            const saleId = Utils.generateId();

            if (this.database.isDatabaseAvailable()) {
                await this.database.createSale({
                    id: saleId,
                    user_id: interaction.user.id,
                    username: interaction.user.username,
                    product_id: productId,
                    amount: product.price,
                    channel_id: channel.id
                });
            } else {
                // Criar venda no Supabase quando SQLite não estiver disponível
                try {
                    await this.supabaseLicenseManager.createSale({
                        id: saleId,
                        user_id: interaction.user.id,
                        username: interaction.user.username,
                        product_id: productId,
                        amount: product.price,
                        channel_id: channel.id,
                        status: 'pending',
                        payment_method: 'pix'
                    });
                    console.log('✅ Venda criada no Supabase');
                } catch (error) {
                    console.error('❌ Erro ao criar venda no Supabase:', error);
                    return interaction.editReply({
                        embeds: [Utils.createErrorEmbed('Erro ao processar compra. Tente novamente.')]
                    });
                }
            }

            // Responder ao usuário imediatamente
            await interaction.editReply({
                embeds: [Utils.createSuccessEmbed(
                    `Carrinho criado: ${channel}\nProssiga com sua compra lá!`,
                    'Compra Iniciada'
                )]
            });

            // Enviar mensagem de boas-vindas no canal privado
            try {
                console.log(`📤 Enviando mensagem de boas-vindas no canal: ${channel.name}`);
                await this.sendWelcomeMessage(channel, interaction.user, product, saleId);
                console.log(`✅ Mensagem de boas-vindas enviada com sucesso`);
            } catch (welcomeError) {
                console.error('❌ ERRO ao enviar mensagem de boas-vindas:', welcomeError);
                // Tentar enviar mensagem simples como fallback
                try {
                    await channel.send(`${interaction.user} Bem-vindo ao seu carrinho! Houve um erro ao carregar a mensagem completa. Entre em contato com o suporte.`);
                } catch (fallbackError) {
                    console.error('❌ ERRO no fallback da mensagem:', fallbackError);
                }
            }

        } catch (error) {
            console.error('Erro ao processar compra:', error);
            // Remover usuário da lista de compras ativas em caso de erro
            this.activePurchases.delete(userId);

            // Verificar se é erro de interação expirada
            if (error.code === 10062 || error.message?.includes('Unknown interaction')) {
                console.log('⚠️ Interação expirada - ignorando resposta');
                return;
            }

            try {
                if (interaction.deferred) {
                    await interaction.editReply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente em alguns instantes.')]
                    });
                } else {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente em alguns instantes.')],
                        flags: 64 // Ephemeral flag
                    });
                }
            } catch (replyError) {
                console.log('⚠️ Erro ao responder interação:', replyError.message);
            }
        }
    }

    async sendWelcomeMessage(channel, user, product, saleId) {
        try {
            console.log(`🔧 Criando embed de boas-vindas para produto: ${product.title}`);

            const embed = new EmbedBuilder()
                .setTitle('<:cart:1399460437572976820> Bem-vindo ao seu carrinho!')
                .setDescription(`Olá ${user}, você está prestes a adquirir **${product.title}**!`)
                .addFields(
                    { name: '<:cardbox:1399850629219880990> Produto', value: product.title || 'N/A', inline: true },
                    { name: '<:coin:1399460496213540884> Valor', value: Utils.formatPrice(product.price) || 'N/A', inline: true },
                    { name: '<:receipt:1399850763487805628> ID da Compra', value: `\`${saleId}\``, inline: true }
                )
                .setColor(Utils.getEmbedColor('Primary'))
                .setTimestamp();

            // Adicionar thumbnail apenas se existir
            if (product.banner_url && product.banner_url !== 'N/A') {
                try {
                    embed.setThumbnail(product.banner_url);
                } catch (thumbnailError) {
                    console.warn('⚠️ Erro ao definir thumbnail, continuando sem ela:', thumbnailError.message);
                }
            }

            console.log(`🔧 Criando embed de termos de uso`);

            const termsEmbed = new EmbedBuilder()
                .setTitle('<:10447information:1399851013371854848> Termos de Uso')
                .setDescription(`
**Ao prosseguir com esta compra, você concorda com:**

• O produto é uma licença de software digital
• Não há reembolso após a entrega da licença
• A licença é pessoal e intransferível
• O uso deve seguir os termos de serviço
• Suporte técnico incluso por 30 dias

**Política de Privacidade:**
• Seus dados são protegidos conforme LGPD
• Não compartilhamos informações com terceiros
• Dados de pagamento processados pelo Mercado Pago
                `)
                .setColor(Utils.getEmbedColor('Info'))
                .setFooter({ text: 'StonePlugins - Termos de Uso' });

            console.log(`🔧 Criando botões de ação`);

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`accept_terms_${saleId}`)
                        .setLabel('Aceitar e Continuar')
                        .setEmoji('<:check:1399460320854151230>')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId(`cancel_purchase_${saleId}`)
                        .setLabel('Cancelar Compra')
                        .setEmoji('<:cancel:1399851210575712448>')
                        .setStyle(ButtonStyle.Danger)
                );

            console.log(`📤 Enviando mensagem completa no canal`);

            await channel.send({
                content: `${user}`,
                embeds: [embed, termsEmbed],
                components: [row]
            });

            console.log(`✅ Mensagem de boas-vindas enviada com sucesso!`);

        } catch (error) {
            console.error('❌ ERRO CRÍTICO na sendWelcomeMessage:', error);
            console.error('Stack trace:', error.stack);

            // Fallback: enviar mensagem simples
            try {
                await channel.send({
                    content: `${user} ⚠️ **Carrinho Criado**\n\n**Produto:** ${product.title}\n**Valor:** ${Utils.formatPrice(product.price)}\n**ID:** \`${saleId}\`\n\nHouve um erro ao carregar a interface completa. Entre em contato com o suporte.`
                });
                console.log(`✅ Mensagem de fallback enviada`);
            } catch (fallbackError) {
                console.error('❌ ERRO CRÍTICO no fallback:', fallbackError);
                throw fallbackError; // Re-throw para ser capturado pelo handler principal
            }
        }
    }

    async handleTermsAcceptance(interaction) {
        const saleId = interaction.customId.replace('accept_terms_', '');
        console.log(`🔧 Processando aceitação de termos para venda: ${saleId}`);
        console.log(`🔍 CustomId completo: ${interaction.customId}`);
        console.log(`👤 Usuário: ${interaction.user.tag} (${interaction.user.id})`);
        console.log(`📍 Canal: ${interaction.channel.name} (${interaction.channel.id})`);
        console.log(`⏰ Timestamp da interação: ${new Date(interaction.createdTimestamp).toISOString()}`);

        try {
            // Verificar se a interação não expirou (15 minutos)
            const interactionAge = Date.now() - interaction.createdTimestamp;
            if (interactionAge > 15 * 60 * 1000) {
                console.log(`⚠️ Interação expirada (${Math.round(interactionAge / 1000)}s) - ignorando termos`);
                return;
            }

            // Verificar se já foi respondida
            if (interaction.replied || interaction.deferred) {
                console.log('⚠️ Interação já foi processada - ignorando');
                return;
            }

            console.log(`✅ Processando aceitação de termos - deferindo resposta`);
            // Defer the update to acknowledge the interaction
            await interaction.deferUpdate();
            console.log(`✅ Resposta deferida com sucesso`);

            // Processando termos (sem loading demorado)
            console.log(`📝 Enviando mensagem de termos aceitos`);
            await interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setTitle('<:check:1399460320854151230> Termos aceitos')
                    .setDescription('Gerando seu pagamento...')
                    .setColor(Utils.getEmbedColor('Success'))],
                components: []
            });
            console.log(`✅ Mensagem de termos aceitos enviada`);

            // Buscar venda (SQLite local ou Supabase)
            console.log(`🔍 Buscando venda com ID: ${saleId}`);
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                console.log(`📊 Buscando venda no SQLite`);
                sale = await this.database.getSale(saleId);
            } else {
                // Buscar venda no Supabase quando SQLite não estiver disponível
                console.log(`☁️ Buscando venda no Supabase`);
                try {
                    sale = await this.supabaseLicenseManager.getSale(saleId);
                    console.log(`✅ Venda encontrada no Supabase:`, sale ? 'Sim' : 'Não');
                } catch (error) {
                    console.error('❌ Erro ao buscar venda no Supabase:', error);
                }
            }

            if (!sale) {
                console.error(`❌ Venda não encontrada para ID: ${saleId}`);
                return interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Compra não encontrada!')],
                    components: []
                });
            }

            console.log(`✅ Venda encontrada: ${sale.id} - Produto: ${sale.product_id}`);

            // Buscar produto (SQLite local ou Supabase)
            console.log(`🔍 Buscando produto com ID: ${sale.product_id}`);
            let product = null;

            if (this.database.isDatabaseAvailable()) {
                console.log(`📊 Buscando produto no SQLite`);
                product = await this.database.getProduct(sale.product_id);
            } else {
                // Buscar produto no Supabase quando SQLite não estiver disponível
                console.log(`☁️ Buscando produto no Supabase`);
                try {
                    const result = await this.supabaseLicenseManager.getProductById(sale.product_id);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price),
                            download_link: result.download_link // ADICIONADO: Link de download do Supabase
                        };
                        console.log(`✅ Produto encontrado no Supabase: ${product.title}`);
                    } else {
                        console.log(`❌ Produto não encontrado no Supabase`);
                    }
                } catch (error) {
                    console.error('❌ Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                console.error(`❌ Produto não encontrado para ID: ${sale.product_id}`);
                return interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            console.log(`✅ Produto encontrado: ${product.title} - R$ ${product.price}`);

            // Gerando PIX (direto)
            console.log(`💳 Atualizando mensagem para "Gerando PIX..."`);
            await interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setTitle('<a:Animated_Loading_3:1399159702079668266> Gerando PIX...')
                    .setDescription('Criando pagamento no Mercado Pago.')
                    .setColor(Utils.getEmbedColor('Warning'))],
                components: []
            });
            console.log(`✅ Mensagem "Gerando PIX..." enviada`);

            // Verificar se há webhook URL configurada
            if (!process.env.WEBHOOK_URL) {
                console.warn('⚠️ WEBHOOK_URL não configurada, criando pagamento sem notificação automática');
            } else {
                console.log(`✅ WEBHOOK_URL configurada: ${process.env.WEBHOOK_URL.substring(0, 50)}...`);
            }

            // Criar pagamento PIX
            console.log(`💳 Criando pagamento PIX para produto: ${product.title} - R$ ${product.price}`);
            let pixPayment;

            try {
                pixPayment = await this.mercadoPago.createPixPayment({
                    amount: product.price,
                    description: `${product.title} - StonePlugins`,
                    external_reference: saleId
                });
                console.log(`✅ Pagamento PIX criado com sucesso: ${pixPayment?.id}`);
            } catch (pixError) {
                console.error('❌ ERRO ao criar pagamento PIX:', pixError);
                console.error('Stack trace:', pixError.stack);

                // Resposta de erro para o usuário
                return interaction.editReply({
                    embeds: [new EmbedBuilder()
                        .setTitle('❌ Erro ao Gerar Pagamento')
                        .setDescription('Houve um erro ao criar o pagamento PIX. Tente novamente em alguns instantes ou entre em contato com o suporte.')
                        .setColor(Utils.getEmbedColor('Error'))
                        .addFields(
                            { name: '🆔 ID da Compra', value: `\`${saleId}\``, inline: true },
                            { name: '📦 Produto', value: product.title, inline: true },
                            { name: '💰 Valor', value: Utils.formatPrice(product.price), inline: true }
                        )
                        .setTimestamp()],
                    components: []
                });
            }

            // Atualizar venda com ID do pagamento
            await this.database.updateSaleStatus(saleId, 'awaiting_payment', pixPayment.id);

            // Se não há QR code, tentar buscar novamente após alguns segundos
            if (!pixPayment.qr_code) {
                console.log('🔄 QR Code não encontrado, tentando buscar novamente...');

                // Aguardar um pouco para o QR Code ser gerado
                await new Promise(resolve => setTimeout(resolve, 50));

                try {
                    const updatedPayment = await this.mercadoPago.getPaymentStatus(pixPayment.id);
                    if (updatedPayment.qr_code) {
                        pixPayment.qr_code = updatedPayment.qr_code;
                        pixPayment.qr_code_base64 = updatedPayment.qr_code_base64;
                        pixPayment.ticket_url = updatedPayment.ticket_url;
                        console.log('✅ QR Code encontrado na segunda tentativa');
                    }
                } catch (error) {
                    console.log('Erro ao buscar QR code atualizado:', error.message);
                }
            }

            // Enviar informações de pagamento imediatamente
            await this.sendPaymentInfo(interaction, product, pixPayment, saleId);

            // Iniciar monitoramento do pagamento
            this.startPaymentMonitoring(pixPayment.id, saleId, interaction.channel);

        } catch (error) {
            console.error('Erro ao processar termos:', error);

            // Verificar se é erro de interação expirada
            if (error.code === 10062 || error.message?.includes('Unknown interaction')) {
                console.log('⚠️ Interação expirada - ignorando resposta');
                return;
            }

            try {
                await interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Erro ao processar pagamento. Tente novamente ou entre em contato com o suporte.')],
                    components: []
                });
            } catch (replyError) {
                console.error('Erro ao responder:', replyError.message);
            }
        }
    }

    async sendPaymentInfo(interaction, product, pixPayment, saleId) {
        const embed = new EmbedBuilder()
            .setTitle('<:pix:1399851781588258899> Pagamento via PIX')
            .setDescription(`**Produto:** ${product.title}\n**Valor:** ${Utils.formatPrice(product.price)}`)
            .addFields(
                { name: '<:clock:1399852173831180368> Tempo Limite', value: '15 minutos', inline: true },
                { name: '<:receipt:1399850763487805628> ID do Pagamento', value: `\`${pixPayment.id}\``, inline: true },
                { name: '<:cloudcog:1399852646894145691> Status', value: 'Aguardando Pagamento', inline: true }
            )
            .setColor(Utils.getEmbedColor('Warning'))
            .setTimestamp();

        const embeds = [embed];
        const files = [];

        if (pixPayment.qr_code) {
            console.log('✅ Exibindo QR Code na embed');

            // Se há QR code base64, criar arquivo temporário e anexar
            if (pixPayment.qr_code_base64) {
                try {
                    console.log('✅ Criando imagem do QR Code');

                    // Criar diretório temp se não existir
                    const tempDir = path.join(__dirname, '..', 'temp');
                    if (!fs.existsSync(tempDir)) {
                        fs.mkdirSync(tempDir, { recursive: true });
                    }

                    // Salvar QR code como arquivo temporário
                    const qrFileName = `qr_${saleId}.png`;
                    const qrFilePath = path.join(tempDir, qrFileName);

                    // Converter base64 para buffer e salvar
                    const qrBuffer = Buffer.from(pixPayment.qr_code_base64, 'base64');
                    fs.writeFileSync(qrFilePath, qrBuffer);

                    // Criar attachment
                    const qrAttachment = new AttachmentBuilder(qrFilePath, { name: qrFileName });
                    files.push(qrAttachment);

                    // Definir a imagem no embed
                    embed.setImage(`attachment://${qrFileName}`);

                    console.log('✅ QR Code anexado como imagem');

                    // Agendar remoção do arquivo temporário
                    setTimeout(() => {
                        try {
                            if (fs.existsSync(qrFilePath)) {
                                fs.unlinkSync(qrFilePath);
                                console.log('🗑️ Arquivo temporário do QR Code removido');
                            }
                        } catch (error) {
                            console.error('Erro ao remover arquivo temporário:', error);
                        }
                    }, 60000); // Remover após 1 minuto

                } catch (error) {
                    console.error('Erro ao criar imagem do QR Code:', error);
                }
            }

            const pixCodeEmbed = new EmbedBuilder()
                .setTitle('<:clipboardcheck:1399852807888175114> Código PIX Copia e Cola')
                .setDescription(`\`\`\`${pixPayment.qr_code}\`\`\``)
                .setColor(Utils.getEmbedColor('Info'))
                .setFooter({ text: 'Copie o código acima e cole no seu app de pagamento' });

            embeds.push(pixCodeEmbed);

            // Se há ticket URL, adicionar como botão
            if (pixPayment.ticket_url) {
                console.log('✅ Ticket URL disponível:', pixPayment.ticket_url);
            }
        } else {
            console.warn('⚠️ QR Code não disponível para exibição');
            // Se não há QR code, mostrar instruções alternativas
            const alternativeEmbed = new EmbedBuilder()
                .setTitle('⚠️ QR Code não disponível')
                .setDescription(`O QR Code PIX não foi gerado automaticamente.\n\n**Opções:**\n• Use o botão "Verificar Pagamento" após fazer o PIX\n• Entre em contato com o suporte\n• Use o comando \`/aprovar ${saleId}\` (apenas admins)\n\n**ID do Pagamento:** \`${pixPayment.id}\``)
                .setColor(Utils.getEmbedColor('Warning'))
                .setFooter({ text: 'Entre em contato com o suporte se precisar de ajuda' });

            embeds.push(alternativeEmbed);
        }

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`cancel_payment_${saleId}`)
                    .setLabel('Cancelar')
                    .setEmoji('<:cancel:1399851210575712448>')
                    .setStyle(ButtonStyle.Secondary)
            );

        const messageOptions = {
            embeds: embeds,
            components: [row]
        };

        if (files.length > 0) {
            messageOptions.files = files;
        }

        await interaction.editReply(messageOptions);
    }

    async startPaymentMonitoring(paymentId, saleId, channel) {
        const checkInterval = setInterval(async () => {
            try {
                const paymentStatus = await this.mercadoPago.getPaymentStatus(paymentId);

                if (this.mercadoPago.isPaymentApproved(paymentStatus.status)) {
                    clearInterval(checkInterval);
                    await this.completePayment(saleId, channel, paymentStatus);
                } else if (this.mercadoPago.isPaymentRejected(paymentStatus.status)) {
                    clearInterval(checkInterval);
                    await this.rejectPayment(saleId, channel, paymentStatus);
                }

            } catch (error) {
                console.error('Erro ao verificar pagamento:', error);
            }
        }, 10000); // Verificar a cada 10 segundos

        // Parar verificação após 20 minutos
        setTimeout(() => {
            clearInterval(checkInterval);
        }, 1200000);

        // Armazenar referência para poder cancelar manualmente
        this.activePayments.set(saleId, checkInterval);
    }

    async completePayment(saleId, channel, paymentStatus, isManualApproval = false) {
        try {
            // Buscar venda (SQLite local ou Supabase)
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                sale = await this.database.getSale(saleId);
            } else {
                try {
                    sale = await this.supabaseLicenseManager.getSale(saleId);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            if (!sale) {
                console.error('Venda não encontrada:', saleId);
                return;
            }

            // Buscar produto (SQLite local ou Supabase)
            let product = null;

            if (this.database.isDatabaseAvailable()) {
                product = await this.database.getProduct(sale.product_id);
                console.log(`🔍 Produto para venda aprovada:`, {
                    id: product?.id,
                    title: product?.title,
                    download_link: product?.download_link
                });
            } else {
                try {
                    const result = await this.supabaseLicenseManager.getProductById(sale.product_id);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link || '',
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                console.error('Produto não encontrado:', sale.product_id);
                return;
            }

            // Remover usuário da lista de compras ativas
            this.activePurchases.delete(sale.user_id);

            // Mostrar loading state no canal (mais rápido)
            const loadingEmbed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Pagamento aprovado!')
                .setDescription('Gerando sua licença...')
                .setColor(Utils.getEmbedColor('Success'));

            await channel.send({
                embeds: [loadingEmbed]
            });

            // Atualizar status da venda
            await this.database.updateSaleStatus(saleId, 'completed', paymentStatus.id || 'manual_approval');

            // Gerar licença
            const licenseKey = Utils.generateLicenseKey();

            // Criar licença no Supabase
            console.log('🔄 [LICENSE] Criando licença no Supabase...');

            try {
                // Criar ou buscar usuário no Supabase
                const supabaseUser = await this.supabaseLicenseManager.createOrGetUser(
                    sale.user_id,
                    sale.username,
                    null // email não disponível
                );

                // Criar ou buscar produto no Supabase
                const supabaseProduct = await this.supabaseLicenseManager.createOrGetProduct(
                    product.title,
                    '1.0.0', // versão padrão
                    product.description,
                    parseFloat(product.price)
                );

                // Criar licença no Supabase
                const supabaseLicense = await this.supabaseLicenseManager.createLicense(
                    licenseKey,
                    supabaseUser.id,
                    supabaseProduct.id,
                    null // licença vitalícia
                );

                console.log('✅ [LICENSE] Licença criada no Supabase com sucesso');
                console.log(`   🔑 License Key: ${licenseKey}`);
                console.log(`   👤 Usuário: ${supabaseUser.username} (${supabaseUser.discord_id})`);
                console.log(`   📦 Produto: ${supabaseProduct.name} v${supabaseProduct.version}`);
                console.log(`   🆔 License ID: ${supabaseLicense.id}`);

            } catch (error) {
                console.error('❌ [LICENSE] Erro ao criar licença no Supabase:', error);
                throw new Error(`Falha ao criar licença no Supabase: ${error.message}`);
            }

            // Atualizar estoque se não for infinito
            if (product.stock > 0) {
                await this.database.updateProductStock(product.id, product.stock - 1);
            }

            // Aguardar um pouco para simular processamento
            await new Promise(resolve => setTimeout(resolve, 100));

            // Mostrar confirmação no canal
            const successEmbed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Pagamento Aprovado!')
                .setDescription('Sua compra foi processada com sucesso!\n\n📧 **Verifique sua DM para receber os dados da licença.**\n\n⏰ **Este canal será fechado automaticamente em 3 segundos.**')
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            // Deletar mensagens anteriores de PIX para limpar o canal
            try {
                const messages = await channel.messages.fetch({ limit: 50 });
                const pixMessages = messages.filter(msg =>
                    msg.embeds.length > 0 &&
                    (msg.embeds[0].title?.includes('PIX') ||
                        msg.embeds[0].description?.includes('Código PIX') ||
                        msg.embeds[0].description?.includes('Aguardando Pagamento'))
                );

                for (const msg of pixMessages.values()) {
                    try {
                        await msg.delete();
                    } catch (deleteError) {
                        console.log('Erro ao deletar mensagem PIX:', deleteError.message);
                    }
                }

                console.log(`🧹 ${pixMessages.size} mensagens de PIX removidas do canal`);
            } catch (error) {
                console.error('Erro ao limpar mensagens de PIX:', error);
            }

            const successMessage = await channel.send({
                embeds: [successEmbed]
            });

            // Iniciar contagem regressiva para fechar o canal
            this.startChannelCloseCountdown(channel, successMessage, 3);

            // Enviar dados completos por DM
            try {
                const user = await channel.client.users.fetch(sale.user_id);

                const dmSuccessEmbed = new EmbedBuilder()
                    .setTitle('<:success:1399460535518363658> Compra Aprovada!')
                    .setDescription('Sua compra foi processada com sucesso!')
                    .addFields(
                        { name: '<:cart:1399460437572976820> Produto', value: product.title, inline: true },
                        { name: '<:coin:1399460496213540884> Valor Pago', value: Utils.formatPrice(sale.amount), inline: true },
                        { name: '<:update:1399460575117053952> Versão', value: product.version || 'v1.0.0', inline: true },
                        { name: '<:info:1399460563611811870> Data', value: Utils.formatDate(new Date()), inline: true },
                        { name: '<:check:1399460320854151230> Sua Licença', value: `\`${licenseKey}\``, inline: false },
                        { name: '<:success:1399460535518363658> Válida até', value: 'LIFETIME (Vitalícia)', inline: true }
                    )
                    .setColor(Utils.getEmbedColor('Success'))
                    .setTimestamp();

                const dmInstructionsEmbed = new EmbedBuilder()
                    .setTitle('<:attach:1399854483428741190> Como usar sua licença')
                    .setDescription(`
**Passos para ativar:**
1. Baixe o plugin usando o botão "Download Plugin" abaixo
2. Coloque o arquivo .jar na pasta \`plugins\` do seu servidor
3. Reinicie o servidor
4. Use o comando: \`/sp license ${licenseKey}\`
5. Pronto! O plugin estará ativo

**Suporte:**
• Discord: [Servidor de Suporte](https://discord.gg/stoneplugins)
• Documentação: https://docs.stoneplugins.com
                    `)
                    .setColor(Utils.getEmbedColor('Info'));

                const dmRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setLabel('Download Plugin')
                            .setEmoji('<:download:1399855327352066159>')
                            .setStyle(ButtonStyle.Link)
                            .setURL(product.download_link || 'https://stoneplugins.com'),
                        new ButtonBuilder()
                            .setCustomId('support_button')
                            .setLabel('Suporte')
                            .setEmoji('<:headphone:1399898843713241150>')
                            .setStyle(ButtonStyle.Link)
                            .setURL('https://discord.com/channels/1399136121325223936/1399553267817775175'),
                        new ButtonBuilder()
                            .setLabel('Documentação')
                            .setEmoji('<:book:1399898951347605565>')
                            .setStyle(ButtonStyle.Link)
                            .setURL('https://docs.stoneplugins.com')
                    );

                console.log(`🔍 Verificando link de download para produto: ${product.title}`);
                console.log(`🔍 download_link no produto: ${product.download_link}`);
                console.log(`🔍 Produto completo:`, JSON.stringify(product, null, 2));

                if (!product.download_link) {
                    console.error(`❌ Produto ${product.title} não tem download_link definido!`);
                } else {
                    console.log(`✅ Link de download válido: ${product.download_link}`);
                }

                // Log da URL que será usada no botão
                const downloadUrl = product.download_link || 'https://stoneplugins.com';
                console.log(`🔗 URL que será usada no botão de download: ${downloadUrl}`);

                // Log detalhado do botão que será enviado
                const downloadButton = dmRow.components.find(btn => btn.data && btn.data.label === '📥 Download Plugin');
                if (downloadButton) {
                    console.log(`🔗 Botão de download criado com URL: ${downloadButton.data.url}`);
                } else {
                    console.error(`❌ Botão de download não encontrado no dmRow!`);
                }

                console.log(`📤 Enviando DM com botão de download`);

                await user.send({
                    embeds: [dmSuccessEmbed, dmInstructionsEmbed],
                    components: [dmRow]
                });

                console.log('✅ Dados da licença enviados por DM');
            } catch (error) {
                console.error('❌ Não foi possível enviar DM para o usuário:', error);

                // Se não conseguir enviar DM, mostrar no canal mesmo
                const fallbackEmbed = new EmbedBuilder()
                    .setTitle('⚠️ Não foi possível enviar DM')
                    .setDescription(`**Sua licença:** \`${licenseKey}\`\n\n**Válida até:** LIFETIME (Vitalícia)\n\n*Ative suas DMs para receber futuras notificações.*`)
                    .setColor(Utils.getEmbedColor('Warning'));

                await channel.send({
                    embeds: [fallbackEmbed]
                });
            }

        } catch (error) {
            console.error('❌ [PAYMENT] Erro ao completar pagamento:', error);

            // Se falhou ao criar licença no sistema externo, notificar no canal
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Erro ao Processar Licença')
                .setDescription(`Houve um erro ao criar sua licença no sistema.\n\n**Erro:** ${error.message}\n\n**Próximos passos:**\n• Entre em contato com o suporte\n• Informe o ID da compra: \`${saleId}\`\n• Seu pagamento foi aprovado e será processado manualmente`)
                .setColor(Utils.getEmbedColor('Danger'))
                .setTimestamp();

            await channel.send({
                embeds: [errorEmbed]
            });

            // Notificar administradores sobre o erro
            console.error(`🚨 [ADMIN-ALERT] Falha ao criar licença para venda ${saleId}`);
            console.error(`   User ID: ${sale.user_id}`);
            console.error(`   Username: ${sale.username}`);
            console.error(`   Product: ${product.title}`);
            console.error(`   Amount: ${sale.amount}`);
            console.error(`   Error: ${error.message}`);
        }
    }

    async rejectPayment(saleId, channel, paymentStatus) {
        try {
            // Buscar venda (SQLite local ou Supabase)
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                sale = await this.database.getSale(saleId);
            } else {
                try {
                    sale = await this.supabaseLicenseManager.getSale(saleId);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            // Remover usuário da lista de compras ativas
            if (sale) {
                this.activePurchases.delete(sale.user_id);
            }

            // Deletar venda rejeitada (SQLite local ou Supabase)
            if (this.database.isDatabaseAvailable()) {
                await this.database.updateSaleStatus(saleId, 'failed');
            } else {
                // Deletar venda do Supabase quando rejeitada
                try {
                    await this.supabaseLicenseManager.deleteSale(saleId);
                    console.log('✅ Venda rejeitada removida do Supabase');
                } catch (error) {
                    console.error('❌ Erro ao deletar venda rejeitada do Supabase:', error);
                }
            }

            const embed = new EmbedBuilder()
                .setTitle('❌ Pagamento Rejeitado')
                .setDescription('Seu pagamento foi rejeitado ou cancelado.')
                .addFields(
                    { name: '📄 Status', value: paymentStatus.status_detail || 'Rejeitado', inline: true },
                    { name: '🔄 Próximos Passos', value: 'Você pode tentar novamente ou entrar em contato com o suporte', inline: false }
                )
                .setColor(Utils.getEmbedColor('Danger'))
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('retry_purchase')
                        .setLabel('🔄 Tentar Novamente')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('contact_support')
                        .setLabel('🎧 Suporte')
                        .setStyle(ButtonStyle.Secondary)
                );

            await channel.send({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro ao processar pagamento rejeitado:', error);
        }
    }

    async handleManualPaymentCheck(interaction) {
        const saleId = interaction.customId.replace('check_payment_', '');

        try {
            // Buscar venda (SQLite local ou Supabase)
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                sale = await this.database.getSale(saleId);
            } else {
                try {
                    sale = await this.supabaseLicenseManager.getSale(saleId);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            if (!sale || !sale.payment_id) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Pagamento não encontrado!')],
                    flags: 64 // Ephemeral flag
                });
            }

            await interaction.deferReply({ flags: 64 }); // Ephemeral flag

            // Verificando pagamento (mais rápido)
            await interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setTitle('🔍 Verificando pagamento...')
                    .setDescription('Consultando Mercado Pago.')
                    .setColor(Utils.getEmbedColor('Warning'))]
            });

            const paymentStatus = await this.mercadoPago.getPaymentStatus(sale.payment_id);

            // Aguardar um pouco para dar sensação de processamento
            await new Promise(resolve => setTimeout(resolve, 100));

            if (this.mercadoPago.isPaymentApproved(paymentStatus.status)) {
                await this.completePayment(saleId, interaction.channel, paymentStatus);
                await interaction.editReply({
                    embeds: [Utils.createSuccessEmbed('Pagamento confirmado! Processando sua compra...')]
                });
            } else if (this.mercadoPago.isPaymentPending(paymentStatus.status)) {
                await interaction.editReply({
                    embeds: [Utils.createWarningEmbed('Pagamento ainda pendente. Aguarde alguns instantes e tente novamente.')]
                });
            } else {
                await interaction.editReply({
                    embeds: [Utils.createErrorEmbed(`Status do pagamento: ${paymentStatus.status_detail || paymentStatus.status}`)]
                });
            }

        } catch (error) {
            console.error('Erro ao verificar pagamento:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao verificar pagamento. Tente novamente.')]
            });
        }
    }

    async handleCancelPurchase(interaction) {
        const saleId = interaction.customId.replace('cancel_purchase_', '').replace('cancel_payment_', '');
        console.log(`🔧 Processando cancelamento para venda: ${saleId}`);

        try {
            // Verificar se a interação não expirou
            const interactionAge = Date.now() - interaction.createdTimestamp;
            if (interactionAge > 15 * 60 * 1000) {
                console.log(`⚠️ Interação expirada (${Math.round(interactionAge / 1000)}s) - ignorando cancelamento`);
                return;
            }

            // Verificar se já foi respondida
            if (interaction.replied || interaction.deferred) {
                console.log('⚠️ Interação já foi processada - ignorando');
                return;
            }

            console.log(`✅ Processando cancelamento - deferindo resposta`);
            // Defer the update to acknowledge the interaction
            await interaction.deferUpdate();

            // Cancelando compra (mais rápido)
            await interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setTitle('<:cancel:1399851210575712448> Cancelando compra...')
                    .setDescription('Processando cancelamento.')
                    .setColor(Utils.getEmbedColor('Error'))],
                components: [],
                files: [] // Limpar arquivos anexados (QR code)
            });

            // Buscar venda (SQLite local ou Supabase)
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                sale = await this.database.getSale(saleId);
            } else {
                try {
                    sale = await this.supabaseLicenseManager.getSale(saleId);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            // Remover usuário da lista de compras ativas
            if (sale) {
                this.activePurchases.delete(sale.user_id);
            }

            // Cancelar monitoramento de pagamento se existir
            if (this.activePayments.has(saleId)) {
                clearInterval(this.activePayments.get(saleId));
                this.activePayments.delete(saleId);
            }

            // Deletar venda (SQLite local ou Supabase)
            if (this.database.isDatabaseAvailable()) {
                await this.database.updateSaleStatus(saleId, 'cancelled');
            } else {
                // Deletar venda do Supabase quando cancelada
                try {
                    await this.supabaseLicenseManager.deleteSale(saleId);
                    console.log('✅ Venda cancelada removida do Supabase');
                } catch (error) {
                    console.error('❌ Erro ao deletar venda do Supabase:', error);
                }
            }

            // Aguardar um pouco para simular processamento
            await new Promise(resolve => setTimeout(resolve, 100));

            const embed = new EmbedBuilder()
                .setTitle('<:check:1399460320854151230> Compra Cancelada')
                .setDescription('Sua compra foi cancelada com sucesso. Este canal será fechado em 5 segundos.')
                .setColor(Utils.getEmbedColor('Secondary'))
                .setTimestamp();

            await interaction.editReply({
                embeds: [embed],
                components: [],
                files: [] // Garantir que não há arquivos anexados
            });

            // Fechar canal em 5 segundos
            console.log(`⏰ Agendando fechamento do canal ${interaction.channel.name} em 5 segundos`);
            setTimeout(async () => {
                try {
                    console.log(`🔄 Tentando fechar canal: ${interaction.channel?.name}`);
                    if (interaction.channel && !interaction.channel.deleted) {
                        // Verificar permissões
                        const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
                        const hasPermission = interaction.channel.permissionsFor(botMember).has('ManageChannels');

                        console.log(`🔑 Bot tem permissão para gerenciar canais: ${hasPermission}`);

                        if (!hasPermission) {
                            console.error(`❌ Bot não tem permissão para deletar o canal ${interaction.channel.name}`);
                            return;
                        }

                        console.log(`🗑️ Fechando canal: ${interaction.channel.name}`);
                        await interaction.channel.delete('Compra cancelada pelo usuário');
                        console.log(`✅ Canal fechado com sucesso`);
                    } else {
                        console.log(`⚠️ Canal já foi deletado ou não existe`);
                    }
                } catch (error) {
                    console.error('❌ Erro ao deletar canal:', error);
                    console.error('Stack trace:', error.stack);

                    // Tentar novamente após 2 segundos
                    setTimeout(async () => {
                        try {
                            if (interaction.channel && !interaction.channel.deleted) {
                                await interaction.channel.delete('Compra cancelada - segunda tentativa');
                                console.log(`✅ Canal fechado na segunda tentativa`);
                            }
                        } catch (retryError) {
                            console.error('❌ Erro na segunda tentativa de fechar canal:', retryError);
                        }
                    }, 2000);
                }
            }, 5000);

        } catch (error) {
            console.error('Erro ao cancelar compra:', error);
            // Try to respond with error if interaction hasn't been responded to yet
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro ao cancelar compra. Tente novamente.')],
                        flags: 64
                    });
                } else {
                    await interaction.editReply({
                        embeds: [Utils.createErrorEmbed('Erro ao cancelar compra. Tente novamente.')],
                        components: [],
                        files: []
                    });
                }
            } catch (responseError) {
                console.error('Erro ao responder com mensagem de erro:', responseError);
            }
        }
    }

    async handleCloseChannel(interaction) {
        try {
            // Buscar venda por channel_id (SQLite local ou Supabase)
            let sale = null;

            if (this.database.isDatabaseAvailable()) {
                sale = await this.database.getSaleByChannelId(interaction.channel.id);
            } else {
                try {
                    sale = await this.supabaseLicenseManager.getSaleByChannelId(interaction.channel.id);
                } catch (error) {
                    console.error('Erro ao buscar venda por channel no Supabase:', error);
                }
            }

            // Remover usuário da lista de compras ativas e limpar venda
            if (sale) {
                this.activePurchases.delete(sale.user_id);

                // Deletar venda pendente quando canal é fechado manualmente
                if (sale.status === 'pending') {
                    if (this.database.isDatabaseAvailable()) {
                        try {
                            await this.database.updateSaleStatus(sale.id, 'cancelled');
                        } catch (error) {
                            console.error('Erro ao cancelar venda no SQLite:', error);
                        }
                    } else {
                        try {
                            await this.supabaseLicenseManager.deleteSale(sale.id);
                            console.log('✅ Venda pendente removida do Supabase (canal fechado manualmente)');
                        } catch (error) {
                            console.error('❌ Erro ao deletar venda do Supabase:', error);
                        }
                    }
                }
            }

            const embed = new EmbedBuilder()
                .setTitle('🔒 Fechando Canal')
                .setDescription('Este canal será fechado em 5 segundos...')
                .setColor(Utils.getEmbedColor('Secondary'));

            await interaction.update({
                embeds: [embed],
                components: []
            });

            console.log(`⏰ Agendando fechamento manual do canal ${interaction.channel.name} em 5 segundos`);
            setTimeout(async () => {
                try {
                    console.log(`🔄 Tentando fechar canal manualmente: ${interaction.channel?.name}`);
                    if (interaction.channel && !interaction.channel.deleted) {
                        // Verificar permissões
                        const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
                        const hasPermission = interaction.channel.permissionsFor(botMember).has('ManageChannels');

                        console.log(`🔑 Bot tem permissão para gerenciar canais: ${hasPermission}`);

                        if (!hasPermission) {
                            console.error(`❌ Bot não tem permissão para deletar o canal ${interaction.channel.name}`);
                            return;
                        }

                        console.log(`🗑️ Fechando canal manualmente: ${interaction.channel.name}`);
                        await interaction.channel.delete('Canal fechado manualmente pelo usuário');
                        console.log(`✅ Canal fechado manualmente com sucesso`);
                    } else {
                        console.log(`⚠️ Canal já foi deletado ou não existe`);
                    }
                } catch (error) {
                    console.error('❌ Erro ao deletar canal manualmente:', error);
                    console.error('Stack trace:', error.stack);
                }
            }, 5000); // 5 segundos
        } catch (error) {
            console.error('Erro ao fechar canal:', error);
        }
    }

    // Iniciar contagem regressiva para fechar o canal automaticamente
    async startChannelCloseCountdown(channel, message, seconds) {
        console.log(`🔄 [AUTO-CLOSE] Iniciando contagem regressiva de ${seconds} segundos para canal ${channel.name} (ID: ${channel.id})`);

        let remainingSeconds = seconds;

        const updateCountdown = async () => {
            try {
                // Verificar se o canal ainda existe
                if (!channel || channel.deleted) {
                    console.log('⚠️ [AUTO-CLOSE] Canal já foi deletado, cancelando contagem regressiva');
                    return;
                }

                if (remainingSeconds <= 0) {
                    console.log('🔒 [AUTO-CLOSE] Tempo esgotado, fechando canal...');

                    // Mostrar mensagem final antes de fechar
                    const finalEmbed = new EmbedBuilder()
                        .setTitle('🔒 Fechando Canal')
                        .setDescription('Canal sendo fechado automaticamente...')
                        .setColor(Utils.getEmbedColor('Secondary'))
                        .setTimestamp();

                    try {
                        await message.edit({
                            embeds: [finalEmbed]
                        });
                        console.log('✅ [AUTO-CLOSE] Mensagem final enviada');
                    } catch (editError) {
                        console.log('⚠️ [AUTO-CLOSE] Erro ao editar mensagem final, prosseguindo com fechamento:', editError.message);
                    }

                    // Aguardar 3 segundos e fechar o canal
                    setTimeout(async () => {
                        try {
                            if (channel && !channel.deleted) {
                                await channel.delete('Pagamento concluído - fechamento automático');
                                console.log(`✅ [AUTO-CLOSE] Canal ${channel.name} fechado automaticamente após pagamento concluído`);
                            } else {
                                console.log('⚠️ [AUTO-CLOSE] Canal já estava deletado');
                            }
                        } catch (error) {
                            console.error('❌ [AUTO-CLOSE] Erro ao deletar canal automaticamente:', error);
                        }
                    }, 3000);

                    return;
                }

                // Atualizar embed com contagem regressiva
                const countdownEmbed = new EmbedBuilder()
                    .setTitle('✅ Pagamento Aprovado!')
                    .setDescription(`Sua compra foi processada com sucesso!\n\n📧 **Verifique sua DM para receber os dados da licença.**\n\n⏰ **Este canal será fechado automaticamente em ${remainingSeconds} segundos.**`)
                    .setColor(Utils.getEmbedColor('Success'))
                    .setTimestamp();

                try {
                    await message.edit({
                        embeds: [countdownEmbed]
                    });
                    console.log(`⏰ [AUTO-CLOSE] Contagem regressiva: ${remainingSeconds} segundos restantes`);
                } catch (editError) {
                    console.error('❌ [AUTO-CLOSE] Erro ao atualizar contagem regressiva:', editError.message);
                    // Se não conseguir editar, continuar a contagem mesmo assim
                }

                remainingSeconds--;

                // Agendar próxima atualização
                setTimeout(updateCountdown, 1000);

            } catch (error) {
                console.error('❌ [AUTO-CLOSE] Erro geral na contagem regressiva:', error);
                // Se houver erro, tentar fechar o canal mesmo assim após 5 segundos
                setTimeout(async () => {
                    try {
                        if (channel && !channel.deleted) {
                            await channel.delete('Erro na contagem regressiva - fechamento forçado');
                            console.log(`✅ [AUTO-CLOSE] Canal ${channel.name} fechado após erro na contagem regressiva`);
                        }
                    } catch (deleteError) {
                        console.error('❌ [AUTO-CLOSE] Erro ao deletar canal após erro na contagem:', deleteError);
                    }
                }, 5000);
            }
        };

        // Iniciar contagem regressiva após 1 segundo
        console.log('🚀 [AUTO-CLOSE] Iniciando contagem regressiva...');
        setTimeout(updateCountdown, 1000);
    }

    // Limpar compras ativas antigas (executar periodicamente)
    cleanupOldPurchases() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30 minutos

        for (const [userId, purchase] of this.activePurchases.entries()) {
            if (now - purchase.timestamp > maxAge) {
                this.activePurchases.delete(userId);
            }
        }
    }
}

module.exports = PurchaseHandler;
