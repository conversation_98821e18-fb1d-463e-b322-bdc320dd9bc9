# ========================================
#     StonePlugins Core - Configuração
# ========================================

# Configurações da API de licenças
api:
  # URL base da API de validação
  base_url: "https://discord-stone-plugins.onrender.com/api"
  
  # Timeout para conexões (em segundos)
  connection_timeout: 10
  read_timeout: 15
  
  # Intervalo de verificação de licenças (em minutos)
  check_interval: 30
  
  # Tentar reconectar se API estiver offline
  auto_reconnect: true
  reconnect_interval: 5

# Configurações de segurança
security:
  # Verificar integridade dos plugins
  check_plugin_integrity: true
  
  # Bloquear plugins não autorizados
  block_unauthorized: true
  
  # Log de tentativas de uso inválido
  log_invalid_attempts: true

# Configurações de logs
logging:
  # Nível de log (INFO, WARNING, SEVERE)
  level: "INFO"
  
  # Log detalhado de verificações
  detailed_checks: false
  
  # Log de comunicação com API
  api_debug: false

# Configurações de cache
cache:
  # Cache de licenças válidas (em minutos)
  license_cache_time: 60
  
  # Cache de verificações de plugins
  plugin_cache_time: 30

# Mensagens personalizadas
messages:
  # Prefixo das mensagens
  prefix: "§6[StonePlugins]§f"
  
  # Mensagem quando licença não é encontrada
  no_license: "§cLicença não encontrada! Use /stoneplugins ativar <licença>"
  
  # Mensagem quando licença é ativada
  license_activated: "§aLicença ativada com sucesso!"
  
  # Mensagem quando licença é desativada
  license_deactivated: "§cLicença desativada!"
  
  # Mensagem de erro de conexão
  connection_error: "§cErro de conexão com servidor de licenças!"

# Configurações avançadas
advanced:
  # Permitir modo offline (sem verificação de API)
  allow_offline_mode: false
  
  # Tempo limite para modo offline (em horas)
  offline_grace_period: 24
  
  # Verificar atualizações automaticamente
  check_updates: true
  
  # Notificar administradores sobre atualizações
  notify_updates: true

# Não modificar - Configurações internas
internal:
  config_version: 1
  first_run: true