﻿#  StonePayments - Mudanças de Comandos

##  Alterações Realizadas

### **<PERSON>mando Principal Alterado**
-  **Antes:** `/stonepayments` ou `/sp` para abrir loja
-  **Agora:** `/comprar` para abrir loja

### **Novos Comandos**

#### **Para Jogadores:**
```
/comprar                    - Abrir menu de compras
/comprar                    - Aliases: /loja, /shop, /pagamentos
/stonepayments info         - Informações do plugin
/stonepayments license      - Status da licença
```

#### **Para Administradores:**
```
/stonepayments reload       - Recarregar configurações
/stonepayments status       - Status completo do sistema
/stoneplugins ativar <key>  - Ativar licença (comando do Core)
```

##  **Funcionalidades por Comando**

### **`/comprar`**
-  Abre o menu de compras
-  Verifica licença automaticamente
-  Mostra erro se licença inativa
-  Funciona apenas para jogadores

### **`/stonepayments info`**
-  Mostra versão do plugin
-  Status da licença
-  Informações básicas
-  Funciona para todos

### **`/stonepayments license`**
-  Status detalhado da licença
-  Instruções para ativação
-  Funciona para todos

### **`/stonepayments reload`** (Admin)
-  Recarrega configurações
-  Apenas para administradores
-  Não reinicia o plugin

### **`/stonepayments status`** (Admin)
-  Status completo do sistema
-  Conexão Mercado Pago
-  Status do banco de dados
-  Apenas para administradores

##  **Sistema de Licenças Integrado**

### **Com Licença Ativa:**
-  `/comprar` funciona normalmente
-  Todas as funcionalidades disponíveis
-  Processamento de pagamentos habilitado

### **Sem Licença:**
-  `/comprar` mostra mensagem de licença necessária
-  Comandos informativos funcionam
-  Pagamentos desabilitados

##  **Arquivos Modificados**

1. **`plugin.yml`** - Comandos atualizados
2. **`StonePayments.java`** - Registro de comandos
3. **`ShopCommand.java`** - Novo comando `/comprar`
4. **`PaymentCommand.java`** - Comandos administrativos
5. **`maven-build-licensed.bat`** - Script atualizado

##  **Como Usar**

### **Para Jogadores:**
```
1. Use /comprar para abrir a loja
2. Escolha o produto desejado
3. Siga as instruções de pagamento
4. Use /stonepayments info para ver status
```

### **Para Administradores:**
```
1. Instale StonePluginsDepend primeiro
2. Ative a licença: /stoneplugins ativar <licença>
3. Configure Mercado Pago no config.yml
4. Use /stonepayments status para verificar
5. Use /stonepayments reload após mudanças
```

##  **Importante**

- **StonePluginsDepend é obrigatório** para funcionar
- **Licença deve estar ativa** para processar pagamentos
- **Comando `/comprar`** é o principal para jogadores
- **Comandos administrativos** requerem permissão
- **Sistema funciona em modo limitado** sem licença

##  **Benefícios das Mudanças**

1. **Comando mais intuitivo** (`/comprar` vs `/stonepayments`)
2. **Separação clara** entre comandos de jogador e admin
3. **Melhor integração** com sistema de licenças
4. **Mensagens mais claras** sobre status da licença
5. **Experiência mais profissional** para o usuário final

---

**Agora o StonePayments tem uma interface mais limpa e intuitiva!** 
