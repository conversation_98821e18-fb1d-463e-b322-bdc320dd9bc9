package com.stoneplugins.core;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Classe para armazenar dados de uma licença
 */
public class LicenseData {
    private String licenseKey;
    private String productName;
    private String expiresAt;
    private String serverUUID;
    private LocalDateTime activatedAt;
    private boolean isActive;
    
    public LicenseData(String licenseKey, String productName, String expiresAt, String serverUUID) {
        this.licenseKey = licenseKey;
        this.productName = productName;
        this.expiresAt = expiresAt;
        this.serverUUID = serverUUID;
        this.activatedAt = LocalDateTime.now();
        this.isActive = true;
    }
    
    // Getters
    public String getLicenseKey() { return licenseKey; }
    public String getProductName() { return productName; }
    public String getExpiresAt() { return expiresAt; }
    public String getServerUUID() { return serverUUID; }
    public LocalDateTime getActivatedAt() { return activatedAt; }
    public boolean isActive() { return isActive; }
    
    // Setters
    public void setActive(boolean active) { this.isActive = active; }
    
    /**
     * Verificar se a licença está expirada
     */
    public boolean isExpired() {
        if (expiresAt == null || expiresAt.equals("null")) {
            return false; // Licença vitalícia
        }
        
        try {
            LocalDateTime expiry = LocalDateTime.parse(expiresAt, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            return LocalDateTime.now().isAfter(expiry);
        } catch (Exception e) {
            return false; // Se não conseguir parsear, considera como não expirada
        }
    }
    
    /**
     * Obter dias restantes até expirar
     */
    public long getDaysUntilExpiry() {
        if (expiresAt == null || expiresAt.equals("null")) {
            return -1; // Licença vitalícia
        }
        
        try {
            LocalDateTime expiry = LocalDateTime.parse(expiresAt, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            return java.time.Duration.between(LocalDateTime.now(), expiry).toDays();
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * Converter para string para salvar em arquivo
     */
    public String toFileString() {
        return String.format("%s|%s|%s|%s|%s|%s", 
            licenseKey, productName, expiresAt, serverUUID, 
            activatedAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), 
            isActive);
    }
    
    /**
     * Criar objeto a partir de string do arquivo
     */
    public static LicenseData fromFileString(String fileString) {
        String[] parts = fileString.split("\\|");
        if (parts.length >= 4) {
            LicenseData data = new LicenseData(parts[0], parts[1], parts[2], parts[3]);
            if (parts.length >= 5) {
                try {
                    data.activatedAt = LocalDateTime.parse(parts[4], DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                } catch (Exception e) {
                    // Usar data atual se não conseguir parsear
                }
            }
            if (parts.length >= 6) {
                data.isActive = Boolean.parseBoolean(parts[5]);
            }
            return data;
        }
        return null;
    }
}