const { <PERSON><PERSON><PERSON>ommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle, StringSelectMenuBuilder } = require('discord.js');
const Utils = require('../utils');
const { SupabaseLicenseManager } = require('../supabase');
const StatusChannelsManager = require('../statusChannels');

// Sistema de debounce para evitar processamento duplo
const processingInteractions = new Set();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('configurar')
        .setDescription('Configurar produtos para venda')
        .setDefaultMemberPermissions('0'),

    async execute(interaction) {
        // Logs apenas em desenvolvimento
        if (process.env.NODE_ENV !== 'production') {
            console.log(`🔧 Comando configurar: ${interaction.user.tag}`);
        }
        
        // Verificar se é admin (pular verificação se ADMIN_ROLE_ID não estiver configurado)
        if (process.env.ADMIN_ROLE_ID && !interaction.member.roles.cache.has(process.env.ADMIN_ROLE_ID)) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed('Você não tem permissão para usar este comando!')],
                flags: 64 // Ephemeral flag
            });
        }

        const embed = new EmbedBuilder()
            .setTitle('<:settings:1399460190335799388> Configuração do Sistema')
            .setDescription('Escolha uma categoria para gerenciar:')
            .addFields(
                { name: '<:cart:1399460437572976820> Configurar Produtos', value: 'Criar, editar, listar e publicar produtos', inline: false },
                { name: '<:check:1399460320854151230> Ações Automáticas', value: 'Ver status dos sistemas automáticos', inline: false },
                { name: '<:announcementc:1399460407172661440> Canais de Status', value: 'Criar canais com estatísticas dinâmicas', inline: false }
            )
            .setColor(Utils.getEmbedColor('Primary'))
            .setTimestamp();

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('main_config_select')
            .setPlaceholder('Selecione uma categoria...')
            .addOptions([
                {
                    label: 'Configurar Produtos',
                    description: 'Criar, editar, listar e publicar produtos',
                    value: 'configure_products',
                    emoji: '<:cart:1399460437572976820>'
                },
                {
                    label: 'Ações Automáticas',
                    description: 'Ver status dos sistemas automáticos',
                    value: 'automatic_actions',
                    emoji: '<:check:1399460320854151230>'
                },
                {
                    label: 'Canais de Status',
                    description: 'Criar canais com estatísticas dinâmicas',
                    value: 'status_channels',
                    emoji: '<:announcementc:1399460407172661440>'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        try {
            // Verificar se a interação ainda é válida (não expirou)
            if (Date.now() - interaction.createdTimestamp > 15 * 60 * 1000) { // 15 minutos
                console.log('⚠️ Interação expirada - ignorando comando');
                return;
            }

            await interaction.reply({
                embeds: [embed],
                components: [row],
                flags: 64 // Ephemeral flag
            });
        } catch (error) {
            console.error('Erro ao executar comando configurar:', error);

            // Verificar se é erro de interação expirada
            if (error.code === 10062 || error.message?.includes('Unknown interaction')) {
                console.log('⚠️ Interação expirada - ignorando resposta');
                return;
            }

            // Tentar responder com erro se possível
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        flags: 64
                    });
                } catch (replyError) {
                    console.error('Erro ao responder interação:', replyError.message);
                }
            }
        }
    },

    async handleButton(interaction, database) {
        try {
            // Verificar se a interação ainda é válida (não expirou)
            const interactionAge = Date.now() - interaction.createdTimestamp;
            if (interactionAge > 14 * 60 * 1000) { // 14 minutos
                console.warn(`⚠️ Interação muito antiga (${Math.round(interactionAge / 1000)}s) - ignorando botão`);
                return;
            }

            // Sistema de debounce - evitar processamento duplo
            const interactionKey = `${interaction.id}_${interaction.user.id}`;
            if (processingInteractions.has(interactionKey)) {
                console.warn(`⚠️ Interação já está sendo processada - ignorando duplicata`);
                return;
            }
            processingInteractions.add(interactionKey);

            // Remover da lista após 30 segundos
            setTimeout(() => {
                processingInteractions.delete(interactionKey);
            }, 30000);

            const customId = interaction.customId;
            console.log(`🔧 HandleButton chamado com customId: ${customId}`);

            switch (customId) {
                case 'create_product':
                    await this.showCreateProductModal(interaction);
                    break;

                case 'list_products':
                    await this.showProductsList(interaction, database);
                    break;

                case 'publish_product':
                    await this.showPublishProductSelect(interaction, database);
                    break;



                case 'create_member_counter':
                    console.log('🔧 Botão create_member_counter clicado');
                    await this.createMemberCounterChannel(interaction);
                    break;

                case 'create_api_status':
                    console.log('🔧 Botão create_api_status clicado');
                    await this.createApiStatusChannel(interaction);
                    break;



                case 'manage_status_channels':
                    console.log('🔧 Botão manage_status_channels clicado');
                    await this.manageStatusChannels(interaction);
                    break;

                case 'update_status_channels':
                    console.log('🔧 Botão update_status_channels clicado');
                    await this.updateStatusChannels(interaction);
                    break;

                case 'delete_all_status_channels':
                    console.log('🔧 Botão delete_all_status_channels clicado');
                    await this.deleteAllStatusChannels(interaction);
                    break;

                case 'edit_product':
                    await this.showEditProductSelect(interaction, database);
                    break;

                case 'delete_product':
                    await this.showDeleteProductSelect(interaction, database);
                    break;

                case 'create_member_counter':
                    await this.createMemberCounterChannel(interaction);
                    break;

                case 'create_api_status':
                    await this.createApiStatusChannel(interaction);
                    break;



                case 'manage_status_channels':
                    await this.manageStatusChannels(interaction);
                    break;

                case 'update_status_channels':
                    await this.updateStatusChannels(interaction);
                    break;

                case 'delete_all_status_channels':
                    await this.deleteAllStatusChannels(interaction);
                    break;

                case 'publish_product':
                    await this.showPublishProductSelect(interaction, database);
                    break;

                case 'cancel_delete':
                    await this.cancelDelete(interaction);
                    break;

                default:
                    if (customId.startsWith('edit_product_')) {
                        const productId = customId.replace('edit_product_', '');
                        await this.showEditOptionsMenu(interaction, database, productId);
                    } else if (customId.startsWith('delete_product_')) {
                        const productId = customId.replace('delete_product_', '');
                        await this.confirmDeleteProduct(interaction, database, productId);
                    } else if (customId.startsWith('publish_product_')) {
                        const productId = customId.replace('publish_product_', '');
                        await this.showChannelSelect(interaction, database, productId);
                    } else if (customId.startsWith('confirm_delete_')) {
                        const productId = customId.replace('confirm_delete_', '');
                        await this.deleteProduct(interaction, database, productId);
                    } else if (customId.startsWith('back_to_settings_')) {
                        const productId = customId.replace('back_to_settings_', '');
                        await this.showProductSettings(interaction, database, productId);
                    }
                    break;
            }

        } catch (error) {
            console.error('❌ Erro ao processar botão:', error);
            console.error('❌ Stack trace:', error.stack);

            // Verificar se é erro de interação expirada
            if (error.code === 10062 || error.message?.includes('Unknown interaction')) {
                console.log('⚠️ Interação expirada - ignorando resposta');
                return;
            }

            // Tentar responder com erro se possível
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed(`Erro interno: ${error.message}`)],
                        flags: 64
                    });
                } else if (interaction.deferred) {
                    await interaction.editReply({
                        embeds: [Utils.createErrorEmbed(`Erro interno: ${error.message}`)]
                    });
                }
            } catch (replyError) {
                console.error('❌ Erro ao responder interação:', replyError.message);
            }
        }
    },

    async showProductsMenu(interaction) {
        try {
            const embed = new EmbedBuilder()
                .setTitle('<:cart:1399460437572976820> Configurar Produtos')
                .setDescription('Escolha uma ação para gerenciar seus produtos:')
                .addFields(
                    { name: '<:add:1399460766398021752> Criar Produto', value: 'Criar um novo produto para venda', inline: true },
                    { name: '<:info:1399460563611811870> Listar Produtos', value: 'Ver todos os produtos criados', inline: true },
                    { name: '<:announcementc:1399460407172661440> Publicar Produto', value: 'Publicar produto em um canal', inline: true },
                    { name: '<:settings:1399460190335799388> Editar Produto', value: 'Editar um produto existente', inline: true },
                    { name: '<:othertrash:1399461117243166914> Excluir Produto', value: 'Remover um produto', inline: true }
                )
                .setColor(Utils.getEmbedColor('Primary'))
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('create_product')
                        .setLabel('Criar Produto')
                        .setEmoji('<:add:1399460766398021752>')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('list_products')
                        .setLabel('Listar Produtos')
                        .setEmoji('<:info:1399460563611811870>')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('publish_product')
                        .setLabel('Publicar Produto')
                        .setEmoji('<:announcementc:1399460407172661440>')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('edit_product')
                        .setLabel('Editar Produto')
                        .setEmoji('<:settings:1399460190335799388>')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('delete_product')
                        .setLabel('Excluir Produto')
                        .setEmoji('<:othertrash:1399461117243166914>')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro ao mostrar menu de produtos:', error);
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro ao carregar menu de produtos.')],
                        flags: 64
                    });
                } else {
                    await interaction.editReply({
                        embeds: [Utils.createErrorEmbed('Erro ao carregar menu de produtos.')],
                        components: []
                    });
                }
            } catch (replyError) {
                console.error('Erro ao responder com erro:', replyError);
            }
        }
    },

    async showAutomaticActions(interaction) {
        try {
            const embed = new EmbedBuilder()
                .setTitle('<:settings:1399460190335799388> Ações Automáticas')
                .setDescription('Status dos sistemas automáticos do bot:')
                .addFields(
                    {
                        name: '<:check:1399460320854151230> Licenças/API Status',
                        value: '🟢 **Online**',
                        inline: true
                    },
                    {
                        name: '<:info:1399460563611811870> Total de Membros',
                        value: `**${interaction.guild.memberCount}**`,
                        inline: true
                    },
                    {
                        name: '<:coin:1399460496213540884> Sistema de Pagamentos',
                        value: '🟢 **Mercado Pago Ativo**',
                        inline: true
                    },
                    {
                        name: '<:success:1399460535518363658> Limpeza Automática',
                        value: '🟢 **Ativa** (Remove vendas antigas)',
                        inline: true
                    },
                    {
                        name: '<:warning:1399460575117053952> Backup de Dados',
                        value: '🟢 **Supabase Conectado**',
                        inline: true
                    },
                    {
                        name: '<:announcementc:1399460407172661440> Notificações',
                        value: process.env.WEBHOOK_URL ? '🟢 **Ativas**' : '🟡 **Webhook não configurado**',
                        inline: true
                    }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.update({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('Erro ao mostrar ações automáticas:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar informações.')],
                flags: 64
            });
        }
    },

    async showStatusChannels(interaction) {
        try {
            const embed = new EmbedBuilder()
                .setTitle('<:announcementc:1399460407172661440> Canais de Status')
                .setDescription('Configure canais que mostram estatísticas em tempo real:')
                .addFields(
                    {
                        name: '<:info:1399460563611811870> Como Funciona',
                        value: 'Os canais de status são canais de voz que mostram informações dinâmicas no nome, atualizadas automaticamente.',
                        inline: false
                    },
                    {
                        name: '<:add:1399460766398021752> Tipos Disponíveis',
                        value: '• **Canal de Membros** - Mostra total de membros do servidor\n• **Canal de API** - Mostra se a API está online (🟢/🔴)',
                        inline: false
                    },
                    {
                        name: '<:warning:1399460575117053952> Importante',
                        value: 'Os canais são atualizados a cada 5 minutos para evitar rate limits do Discord.',
                        inline: false
                    }
                )
                .setColor(Utils.getEmbedColor('Primary'))
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('create_member_counter')
                        .setLabel('Definir Canal de Membros')
                        .setEmoji('<:info:1399460563611811870>')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('create_api_status')
                        .setLabel('Definir Canal de API')
                        .setEmoji('<:check:1399460320854151230>')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('manage_status_channels')
                        .setLabel('Gerenciar Canais')
                        .setEmoji('<:settings:1399460190335799388>')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro ao mostrar canais de status:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar canais de status.')],
                flags: 64
            });
        }
    },

    async createMemberCounterChannel(interaction) {
        try {
            console.log('🔧 Iniciando createMemberCounterChannel');

            const statusManager = new StatusChannelsManager();

            // Verificar se já existe um canal de membros
            const existingChannelId = statusManager.getChannel('memberCounter');
            if (existingChannelId) {
                const existingChannel = interaction.guild.channels.cache.get(existingChannelId);
                if (existingChannel) {
                    return await interaction.reply({
                        embeds: [Utils.createErrorEmbed(`Já existe um canal de membros definido: ${existingChannel}`)],
                        flags: 64
                    });
                }
            }

            // Mostrar seleção de categoria
            const categories = interaction.guild.channels.cache.filter(c => c.type === 4); // Category channels

            if (categories.size === 0) {
                return await interaction.reply({
                    embeds: [Utils.createErrorEmbed('Nenhuma categoria encontrada no servidor.')],
                    flags: 64
                });
            }

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_category_members')
                .setPlaceholder('Selecione a categoria para criar o canal...')
                .addOptions(
                    categories.map(category => ({
                        label: category.name,
                        description: `Criar canal na categoria: ${category.name}`,
                        value: category.id,
                        emoji: '<:info:1399460563611811870>'
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setTitle('<:info:1399460563611811870> Selecionar Categoria')
                .setDescription('Escolha em qual categoria criar o canal de membros:')
                .setColor(Utils.getEmbedColor('Primary'));

            await interaction.reply({
                embeds: [embed],
                components: [row],
                flags: 64
            });

        } catch (error) {
            console.error('Erro ao criar canal de membros:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao criar canal. Verifique as permissões do bot.')],
                flags: 64
            });
        }
    },

    async createApiStatusChannel(interaction) {
        try {
            console.log('🔧 Iniciando createApiStatusChannel');

            const statusManager = new StatusChannelsManager();

            // Verificar se já existe um canal de API
            const existingChannelId = statusManager.getChannel('apiStatus');
            if (existingChannelId) {
                const existingChannel = interaction.guild.channels.cache.get(existingChannelId);
                if (existingChannel) {
                    return await interaction.reply({
                        embeds: [Utils.createErrorEmbed(`Já existe um canal de API definido: ${existingChannel}`)],
                        flags: 64
                    });
                }
            }

            // Mostrar seleção de categoria
            const categories = interaction.guild.channels.cache.filter(c => c.type === 4); // Category channels

            if (categories.size === 0) {
                return await interaction.reply({
                    embeds: [Utils.createErrorEmbed('Nenhuma categoria encontrada no servidor.')],
                    flags: 64
                });
            }

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_category_api')
                .setPlaceholder('Selecione a categoria para criar o canal...')
                .addOptions(
                    categories.map(category => ({
                        label: category.name,
                        description: `Criar canal na categoria: ${category.name}`,
                        value: category.id,
                        emoji: '<:check:1399460320854151230>'
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setTitle('<:check:1399460320854151230> Selecionar Categoria')
                .setDescription('Escolha em qual categoria criar o canal de API:')
                .setColor(Utils.getEmbedColor('Primary'));

            await interaction.reply({
                embeds: [embed],
                components: [row],
                flags: 64
            });

        } catch (error) {
            console.error('Erro ao criar canal de API:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao criar canal. Verifique as permissões do bot.')],
                flags: 64
            });
        }
    },



    async manageStatusChannels(interaction) {
        try {
            await interaction.deferReply({ flags: 64 });

            const statusManager = new StatusChannelsManager();
            const statusChannels = statusManager.getChannels();
            const guild = interaction.guild;

            let description = 'Canais de status ativos:\n\n';
            let hasChannels = false;

            if (statusChannels.memberCounter) {
                const channel = guild.channels.cache.get(statusChannels.memberCounter);
                if (channel) {
                    description += `<:info:1399460563611811870> **Canal de Membros:** ${channel}\n`;
                    hasChannels = true;
                } else {
                    // Canal foi deletado, remover da configuração
                    statusManager.removeChannel('memberCounter');
                }
            }

            if (statusChannels.apiStatus) {
                const channel = guild.channels.cache.get(statusChannels.apiStatus);
                if (channel) {
                    description += `<:check:1399460320854151230> **Canal de API:** ${channel}\n`;
                    hasChannels = true;
                } else {
                    // Canal foi deletado, remover da configuração
                    statusManager.removeChannel('apiStatus');
                }
            }

            if (!hasChannels) {
                description = 'Nenhum canal de status ativo.';
            }

            const embed = new EmbedBuilder()
                .setTitle('<:settings:1399460190335799388> Gerenciar Canais de Status')
                .setDescription(description)
                .setColor(Utils.getEmbedColor('Primary'));

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('update_status_channels')
                        .setLabel('Atualizar Agora')
                        .setEmoji('<:check:1399460320854151230>')
                        .setStyle(ButtonStyle.Success)
                        .setDisabled(!hasChannels),
                    new ButtonBuilder()
                        .setCustomId('delete_all_status_channels')
                        .setLabel('Remover Todos')
                        .setEmoji('<:othertrash:1399461117243166914>')
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(!hasChannels)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('Erro ao gerenciar canais de status:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar canais de status.')]
            });
        }
    },

    async updateStatusChannels(interaction) {
        try {
            await interaction.deferReply({ flags: 64 });

            const statusManager = new StatusChannelsManager();
            const statusChannels = statusManager.getChannels();
            const guild = interaction.guild;
            let updatedCount = 0;

            // Atualizar contador de membros
            if (statusChannels.memberCounter) {
                const channel = guild.channels.cache.get(statusChannels.memberCounter);
                if (channel) {
                    const memberCount = guild.memberCount;
                    const newName = `Membros no Discord: ${memberCount}`;
                    if (channel.name !== newName) {
                        await channel.setName(newName);
                        console.log(`📊 Canal de membros atualizado: ${newName}`);
                    }
                    updatedCount++;
                }
            }

            // Atualizar status da API
            if (statusChannels.apiStatus) {
                const channel = guild.channels.cache.get(statusChannels.apiStatus);
                if (channel) {
                    // Por enquanto, sempre mostrar como online se o bot está rodando
                    const apiStatus = '🟢';
                    const newName = `Licenças/API Status: ${apiStatus}`;
                    if (channel.name !== newName) {
                        await channel.setName(newName);
                        console.log(`🔗 Canal de API atualizado: ${newName}`);
                    }
                    updatedCount++;
                }
            }



            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Canais Atualizados!')
                .setDescription(`${updatedCount} canais de status foram atualizados com sucesso.`)
                .setColor(Utils.getEmbedColor('Success'));

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro ao atualizar canais de status:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao atualizar canais de status.')]
            });
        }
    },

    async deleteAllStatusChannels(interaction) {
        try {
            await interaction.deferReply({ flags: 64 });

            const statusManager = new StatusChannelsManager();
            const statusChannels = statusManager.getChannels();
            const guild = interaction.guild;
            let deletedCount = 0;

            // Deletar todos os canais de status
            for (const [type, channelId] of Object.entries(statusChannels)) {
                const channel = guild.channels.cache.get(channelId);
                if (channel) {
                    try {
                        await channel.delete();
                        deletedCount++;
                        console.log(`🗑️ Canal ${type} deletado: ${channel.name}`);
                    } catch (error) {
                        console.error(`Erro ao deletar canal ${type}:`, error);
                    }
                }
            }

            // Limpar configuração
            statusManager.removeAllChannels();

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Canais Removidos!')
                .setDescription(`${deletedCount} canais de status foram removidos com sucesso.`)
                .setColor(Utils.getEmbedColor('Success'));

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Erro ao deletar canais de status:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao remover canais de status.')]
            });
        }
    },

    async createMemberChannelInCategory(interaction, categoryId) {
        try {
            await interaction.deferUpdate();

            const statusManager = new StatusChannelsManager();
            const category = interaction.guild.channels.cache.get(categoryId);

            if (!category) {
                return await interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Categoria não encontrada.')],
                    components: []
                });
            }

            const memberCount = interaction.guild.memberCount;
            const channelName = `Membros no Discord: ${memberCount}`;

            const channel = await interaction.guild.channels.create({
                name: channelName,
                type: 2, // Voice channel
                parent: categoryId,
                permissionOverwrites: [
                    {
                        id: interaction.guild.roles.everyone,
                        deny: ['Connect', 'Speak']
                    }
                ]
            });

            // Salvar ID do canal
            statusManager.addChannel('memberCounter', channel.id);

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Canal Criado!')
                .setDescription(`Canal de membros criado com sucesso: ${channel}`)
                .addFields(
                    { name: '<:info:1399460563611811870> Categoria', value: category.name, inline: true },
                    { name: '<:info:1399460563611811870> Atualização', value: 'O canal será atualizado automaticamente a cada 5 minutos', inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'));

            await interaction.editReply({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('Erro ao criar canal de membros:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao criar canal. Verifique as permissões do bot.')],
                components: []
            });
        }
    },

    async createApiChannelInCategory(interaction, categoryId) {
        try {
            await interaction.deferUpdate();

            const statusManager = new StatusChannelsManager();
            const category = interaction.guild.channels.cache.get(categoryId);

            if (!category) {
                return await interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Categoria não encontrada.')],
                    components: []
                });
            }

            const channelName = `Licenças/API Status: 🟢`;

            const channel = await interaction.guild.channels.create({
                name: channelName,
                type: 2, // Voice channel
                parent: categoryId,
                permissionOverwrites: [
                    {
                        id: interaction.guild.roles.everyone,
                        deny: ['Connect', 'Speak']
                    }
                ]
            });

            // Salvar ID do canal
            statusManager.addChannel('apiStatus', channel.id);

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Canal Criado!')
                .setDescription(`Canal de API criado com sucesso: ${channel}`)
                .addFields(
                    { name: '<:check:1399460320854151230> Categoria', value: category.name, inline: true },
                    { name: '<:info:1399460563611811870> Atualização', value: 'O canal será atualizado automaticamente a cada 5 minutos', inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'));

            await interaction.editReply({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('Erro ao criar canal de API:', error);
            await interaction.editReply({
                embeds: [Utils.createErrorEmbed('Erro ao criar canal. Verifique as permissões do bot.')],
                components: []
            });
        }
    },

    async handleSelectMenu(interaction, database) {
        try {
            // Verificar se a interação não é muito antiga (mais de 14 minutos)
            const interactionAge = Date.now() - interaction.createdTimestamp;
            if (interactionAge > 14 * 60 * 1000) {
                console.warn(`⚠️ Interação muito antiga (${Math.round(interactionAge / 1000)}s) - ignorando`);
                return;
            }

            // Sistema de debounce - evitar processamento duplo
            const interactionKey = `${interaction.id}_${interaction.user.id}`;
            if (processingInteractions.has(interactionKey)) {
                console.warn(`⚠️ Interação já está sendo processada - ignorando duplicata`);
                return;
            }
            processingInteractions.add(interactionKey);

            // Remover da lista após 30 segundos
            setTimeout(() => {
                processingInteractions.delete(interactionKey);
            }, 30000);

            const customId = interaction.customId;
            const selectedValue = interaction.values[0];

            console.log(`🔧 HandleSelectMenu chamado: customId=${customId}, selectedValue=${selectedValue}`);

            if (customId === 'main_config_select') {
                switch (selectedValue) {
                    case 'configure_products':
                        await this.showProductsMenu(interaction);
                        break;
                    case 'automatic_actions':
                        await this.showAutomaticActions(interaction);
                        break;
                    case 'status_channels':
                        await this.showStatusChannels(interaction);
                        break;
                }
            } else if (customId.startsWith('select_product_publish_')) {
                await this.showChannelSelect(interaction, database, selectedValue);
            } else if (customId.startsWith('select_product_edit_')) {
                await this.showEditOptionsMenu(interaction, database, selectedValue);
            } else if (customId.startsWith('select_product_delete_')) {
                await this.confirmDeleteProduct(interaction, database, selectedValue);
            } else if (customId.startsWith('select_channel_')) {
                const productId = customId.replace('select_channel_', '');
                await this.publishProduct(interaction, database, productId, interaction.guild.channels.cache.get(selectedValue));
            } else if (customId === 'select_category_members') {
                await this.createMemberChannelInCategory(interaction, selectedValue);
            } else if (customId === 'select_category_api') {
                await this.createApiChannelInCategory(interaction, selectedValue);
            } else if (customId.startsWith('edit_option_')) {
                const productId = customId.replace('edit_option_', '');
                await this.handleEditOption(interaction, database, productId, selectedValue);
            } else if (customId.startsWith('product_settings_')) {
                const productId = customId.replace('product_settings_', '');
                await this.handleProductSettings(interaction, database, productId, selectedValue);
            } else if (customId.startsWith('changelog_channel_')) {
                const productId = customId.replace('changelog_channel_', '');
                await this.setChangelogChannel(interaction, database, productId, selectedValue);
            } else if (customId.startsWith('announcement_role_')) {
                const productId = customId.replace('announcement_role_', '');
                await this.setAnnouncementRole(interaction, database, productId, selectedValue);
            }

        } catch (error) {
            console.error('Erro ao processar select menu:', error);

            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        flags: 64
                    });
                } catch (replyError) {
                    console.error('Erro ao responder select menu:', replyError.message);
                }
            }
        }
    },

    async showCreateProductModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('create_product_modal')
            .setTitle('➕ Criar Novo Produto');

        const titleInput = new TextInputBuilder()
            .setCustomId('product_title')
            .setLabel('Título do Produto')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: StonePayments Premium')
            .setRequired(true)
            .setMaxLength(100);

        const descriptionInput = new TextInputBuilder()
            .setCustomId('product_description')
            .setLabel('Descrição do Produto')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Descreva seu produto...')
            .setRequired(true)
            .setMaxLength(1000);

        const priceInput = new TextInputBuilder()
            .setCustomId('product_price')
            .setLabel('Preço (em R$)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 49.90')
            .setRequired(true);

        const versionInput = new TextInputBuilder()
            .setCustomId('product_version')
            .setLabel('Versão do Plugin')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: v1.0.0')
            .setRequired(true)
            .setMaxLength(20);

        const downloadLinkInput = new TextInputBuilder()
            .setCustomId('download_link')
            .setLabel('Link de Download do Plugin')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: https://cdn.discordapp.com/attachments/...')
            .setRequired(true)
            .setMaxLength(500);

        const firstRow = new ActionRowBuilder().addComponents(titleInput);
        const secondRow = new ActionRowBuilder().addComponents(descriptionInput);
        const thirdRow = new ActionRowBuilder().addComponents(priceInput);
        const fourthRow = new ActionRowBuilder().addComponents(versionInput);
        const fifthRow = new ActionRowBuilder().addComponents(downloadLinkInput);

        modal.addComponents(firstRow, secondRow, thirdRow, fourthRow, fifthRow);

        await interaction.showModal(modal);
    },

    async handleCreateProductModal(interaction, database) {
        const title = interaction.fields.getTextInputValue('product_title');
        const description = interaction.fields.getTextInputValue('product_description');
        const priceStr = interaction.fields.getTextInputValue('product_price');
        const version = interaction.fields.getTextInputValue('product_version');
        const downloadLink = interaction.fields.getTextInputValue('download_link');

        // Validar preço
        const price = parseFloat(priceStr.replace(',', '.'));
        if (isNaN(price) || price <= 0) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed('Preço inválido! Use apenas números (ex: 49.90)')],
                flags: 64 // Ephemeral flag
            });
        }

        // Validar link de download
        if (!downloadLink.startsWith('http://') && !downloadLink.startsWith('https://')) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed('Link de download deve começar com http:// ou https://')],
                flags: 64
            });
        }

        // Validar dados
        const validationErrors = Utils.validateProductData({
            title,
            description,
            price,
            version,
            download_link: downloadLink
        });

        if (validationErrors.length > 0) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed(validationErrors.join('\n'))],
                flags: 64 // Ephemeral flag
            });
        }

        try {
            let productId;
            let createdProduct;

            // Criar produto no banco local (SQLite) ou Supabase
            if (database.isDatabaseAvailable()) {
                productId = Utils.generateId();
                await database.createProduct({
                    id: productId,
                    title: Utils.sanitizeInput(title),
                    description: Utils.sanitizeInput(description),
                    version: Utils.sanitizeInput(version),
                    download_link: downloadLink,
                    button_text: 'Comprar Agora',
                    button_color: 'Success',
                    price: price,
                    stock: -1 // Infinito por padrão
                });
                createdProduct = { id: productId, title, description, price };
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                createdProduct = await supabaseLicenseManager.createOrGetProduct(
                    Utils.sanitizeInput(title),
                    version || 'v1.0.0',
                    Utils.sanitizeInput(description),
                    price,
                    downloadLink
                );
                productId = createdProduct.id;
                console.log('✅ Produto criado no Supabase com ID:', productId);
            }

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Produto Criado!')
                .setDescription(`**${title}** criado com sucesso e está pronto para venda!`)
                .addFields(
                    { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(price), inline: true },
                    { name: '<:info:1399460563611811870> ID', value: `\`${productId}\``, inline: true },
                    { name: '<:settings:1399460190335799388> Versão', value: version, inline: true },
                    { name: '<:link:1399460766398021752> Link de Download', value: `[Clique aqui](${downloadLink})`, inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            // Verificar se a interação já foi respondida
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    embeds: [embed],
                    flags: 64 // Ephemeral flag
                });
            }

        } catch (error) {
            console.error('Erro ao criar produto:', error);

            // Verificar se a interação já foi respondida antes de tentar responder
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno ao criar produto. Tente novamente.')],
                        flags: 64 // Ephemeral flag
                    });
                } catch (replyError) {
                    console.error('Erro ao responder interação:', replyError.message);
                }
            } else {
                console.log('⚠️ Interação já foi respondida - não é possível enviar erro');
            }
        }
    },

    async showProductsList(interaction, database) {
        try {
            let products = [];

            if (database.isDatabaseAvailable()) {
                products = await database.getAllProducts();
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getAllProducts();
                    // Converter formato Supabase para formato esperado
                    products = result.map(product => ({
                        id: product.id,
                        title: product.name,
                        description: product.description,
                        price: parseFloat(product.price),
                        stock: -1, // Infinito para produtos do Supabase
                        created_at: product.created_at
                    }));
                } catch (error) {
                    console.error('Erro ao buscar produtos do Supabase:', error);
                    products = [];
                }
            }

            if (products.length === 0) {
                // Verificar se a interação já foi respondida
                if (!interaction.replied && !interaction.deferred) {
                    return interaction.update({
                        embeds: [Utils.createInfoEmbed('Nenhum produto encontrado. Crie seu primeiro produto!')],
                        components: []
                    });
                } else {
                    console.log('⚠️ Interação já foi respondida - não é possível atualizar');
                    return;
                }
            }

            const embed = new EmbedBuilder()
                .setTitle('📋 Lista de Produtos')
                .setDescription(`Total de produtos: **${products.length}**`)
                .setColor(Utils.getEmbedColor('Info'))
                .setTimestamp();

            products.forEach((product, index) => {
                const stockText = product.stock === -1 ? 'Infinito' : product.stock.toString();
                embed.addFields({
                    name: `${index + 1}. ${product.title}`,
                    value: `💰 ${Utils.formatPrice(product.price)} | 📦 ${stockText} | 🆔 \`${product.id}\``,
                    inline: false
                });
            });

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('edit_product')
                        .setLabel('✏️ Editar')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('delete_product')
                        .setLabel('🗑️ Excluir')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('publish_product')
                        .setLabel('📤 Publicar')
                        .setStyle(ButtonStyle.Primary)
                );

            // Verificar se a interação já foi respondida antes de atualizar
            if (!interaction.replied && !interaction.deferred) {
                await interaction.update({
                    embeds: [embed],
                    components: [row]
                });
            } else {
                console.log('⚠️ Interação já foi respondida - não é possível atualizar');
            }

        } catch (error) {
            console.error('Erro ao listar produtos:', error);

            // Verificar se a interação já foi respondida antes de tentar responder
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.update({
                        embeds: [Utils.createErrorEmbed('Erro ao carregar produtos.')],
                        components: []
                    });
                } catch (updateError) {
                    console.error('Erro ao atualizar interação:', updateError.message);
                }
            } else {
                console.log('⚠️ Interação já foi respondida - não é possível mostrar erro');
            }
        }
    },

    async showStats(interaction, database) {
        try {
            const stats = await database.getStats();

            const embed = new EmbedBuilder()
                .setTitle('📊 Estatísticas de Vendas')
                .addFields(
                    { name: '💰 Receita Total', value: Utils.formatPrice(stats.total_revenue || 0), inline: true },
                    { name: '📈 Total de Vendas', value: stats.total_sales.toString(), inline: true },
                    { name: '✅ Vendas Concluídas', value: stats.completed_sales.toString(), inline: true },
                    { name: '⏳ Vendas Pendentes', value: stats.pending_sales.toString(), inline: true },
                    { name: '📊 Taxa de Conversão', value: `${((stats.completed_sales / stats.total_sales) * 100 || 0).toFixed(1)}%`, inline: true },
                    { name: '📅 Última Atualização', value: Utils.formatDate(new Date()), inline: true }
                )
                .setColor(Utils.getEmbedColor('Info'))
                .setTimestamp();

            await interaction.update({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao carregar estatísticas.')],
                components: []
            });
        }
    },

    async showChannelSelect(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64 // Ephemeral flag
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('📤 Publicar Produto')
                .setDescription(`Onde você deseja publicar **${product.title}**?\n\nMencione o canal onde deseja publicar o produto.`)
                .setColor(Utils.getEmbedColor('Primary'));

            await interaction.reply({
                embeds: [embed],
                flags: 64 // Ephemeral flag
            });

            // Aguardar resposta do usuário
            const filter = m => m.author.id === interaction.user.id;
            const collector = interaction.channel.createMessageCollector({ filter, time: 30000, max: 1 });

            collector.on('collect', async (message) => {
                const channelMention = message.mentions.channels.first();
                if (!channelMention) {
                    return message.reply('❌ Por favor, mencione um canal válido!');
                }

                await this.publishProduct(message, database, productId, channelMention);
                message.delete().catch(() => { });
            });

            collector.on('end', collected => {
                if (collected.size === 0) {
                    interaction.followUp({
                        embeds: [Utils.createWarningEmbed('Tempo esgotado! Tente novamente.')],
                        flags: 64 // Ephemeral flag
                    });
                }
            });

        } catch (error) {
            console.error('Erro ao mostrar seleção de canal:', error);
        }
    },

    async publishProduct(message, database, productId, channel) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price),
                            version: result.version || 'v1.0.0',
                            stock: -1, // Infinito para produtos do Supabase
                            button_text: 'Comprar Agora',
                            button_color: 'Success',
                            banner_url: null
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return message.reply('❌ Produto não encontrado!');
            }

            const embed = new EmbedBuilder()
                .setTitle(product.title)
                .setDescription(product.description)
                .addFields(
                    { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(product.price), inline: true },
                    { name: '<:codeterminal:1399897273722212352> Versão', value: product.version || '1.0', inline: true }
                )
                .setColor(Utils.getEmbedColor(product.button_color))
                .setTimestamp();

            if (product.banner_url) {
                embed.setImage(product.banner_url);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`buy_product_${productId}`)
                        .setLabel(product.button_text)
                        .setStyle(Utils.getButtonStyle(product.button_color))
                        .setEmoji('<:cart:1399460437572976820>')
                );

            const publishedMessage = await channel.send({
                embeds: [embed],
                components: [row]
            });

            // Salvar informações da mensagem publicada para futuras atualizações
            try {
                console.log(`💾 Tentando salvar mensagem publicada: ${publishedMessage.id}`);

                if (database.isDatabaseAvailable()) {
                    await database.savePublishedMessage({
                        message_id: publishedMessage.id,
                        channel_id: channel.id,
                        product_id: productId,
                        guild_id: channel.guild.id
                    });
                } else {
                    const { SupabaseLicenseManager } = require('../supabase');
                    const supabaseLicenseManager = new SupabaseLicenseManager();
                    await supabaseLicenseManager.savePublishedMessage({
                        message_id: publishedMessage.id,
                        channel_id: channel.id,
                        product_id: productId,
                        guild_id: channel.guild.id
                    });
                }
                console.log(`✅ Mensagem publicada salva com sucesso: ${publishedMessage.id}`);
            } catch (saveError) {
                console.error('❌ Erro ao salvar mensagem publicada:', saveError);

                if (saveError.message && saveError.message.includes('does not exist')) {
                    console.log('🚨 TABELA FALTANDO! Execute:');
                    console.log('node fix-supabase-tables.js');
                    console.log('OU execute o SQL no painel do Supabase');
                }

                // Não falhar a publicação por causa disso
                console.log('⚠️ Produto publicado, mas não será possível atualizar automaticamente');
            }

            await message.reply(`✅ Produto **${product.title}** publicado com sucesso em ${channel}!`);

        } catch (error) {
            console.error('Erro ao publicar produto:', error);
            await message.reply('❌ Erro ao publicar produto. Verifique as permissões do bot no canal.');
        }
    },

    async showPublishProductSelect(interaction, database) {
        try {
            let products = [];

            if (database.isDatabaseAvailable()) {
                products = await database.getAllProducts();
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getAllProducts();
                    products = result.map(product => ({
                        id: product.id,
                        title: product.name,
                        description: product.description,
                        price: parseFloat(product.price)
                    }));
                } catch (error) {
                    console.error('Erro ao buscar produtos do Supabase:', error);
                    products = [];
                }
            }

            if (products.length === 0) {
                return interaction.reply({
                    embeds: [Utils.createInfoEmbed('Nenhum produto encontrado. Crie seu primeiro produto!')],
                    flags: 64 // Ephemeral flag
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('📤 Selecionar Produto para Publicar')
                .setDescription('Escolha qual produto você deseja publicar:')
                .setColor(Utils.getEmbedColor('Primary'));

            const buttons = [];
            products.slice(0, 5).forEach((product, index) => {
                buttons.push(
                    new ButtonBuilder()
                        .setCustomId(`publish_product_${product.id}`)
                        .setLabel(`${index + 1}. ${product.title.substring(0, 50)}`)
                        .setStyle(ButtonStyle.Secondary)
                );
            });

            const rows = [];
            for (let i = 0; i < buttons.length; i += 5) {
                const row = new ActionRowBuilder()
                    .addComponents(buttons.slice(i, i + 5));
                rows.push(row);
            }

            await interaction.reply({
                embeds: [embed],
                components: rows,
                flags: 64 // Ephemeral flag
            });

        } catch (error) {
            console.error('Erro ao mostrar seleção de produtos:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar produtos.')],
                flags: 64 // Ephemeral flag
            });
        }
    },

    async showEditProductSelect(interaction, database) {
        try {
            let products = [];

            if (database.isDatabaseAvailable()) {
                products = await database.getAllProducts();
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getAllProducts();
                    products = result.map(product => ({
                        id: product.id,
                        title: product.name,
                        description: product.description,
                        price: parseFloat(product.price)
                    }));
                } catch (error) {
                    console.error('Erro ao buscar produtos do Supabase:', error);
                    products = [];
                }
            }

            if (products.length === 0) {
                return interaction.update({
                    embeds: [Utils.createInfoEmbed('Nenhum produto encontrado. Crie seu primeiro produto!')],
                    components: []
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('✏️ Selecionar Produto para Editar')
                .setDescription('Escolha qual produto você deseja editar:')
                .setColor(Utils.getEmbedColor('Primary'));

            const buttons = [];
            products.slice(0, 5).forEach((product, index) => {
                buttons.push(
                    new ButtonBuilder()
                        .setCustomId(`edit_product_${product.id}`)
                        .setLabel(`${index + 1}. ${product.title.substring(0, 50)}`)
                        .setStyle(ButtonStyle.Secondary)
                );
            });

            const rows = [];
            for (let i = 0; i < buttons.length; i += 5) {
                const row = new ActionRowBuilder()
                    .addComponents(buttons.slice(i, i + 5));
                rows.push(row);
            }

            await interaction.update({
                embeds: [embed],
                components: rows
            });

        } catch (error) {
            console.error('Erro ao mostrar seleção de produtos:', error);
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro ao carregar produtos.')],
                        flags: 64
                    });
                } else {
                    await interaction.editReply({
                        embeds: [Utils.createErrorEmbed('Erro ao carregar produtos.')],
                        components: []
                    });
                }
            } catch (replyError) {
                console.error('Erro ao responder com erro:', replyError);
            }
        }
    },

    async showDeleteProductSelect(interaction, database) {
        try {
            let products = [];

            if (database.isDatabaseAvailable()) {
                products = await database.getAllProducts();
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getAllProducts();
                    products = result.map(product => ({
                        id: product.id,
                        title: product.name,
                        description: product.description,
                        price: parseFloat(product.price)
                    }));
                } catch (error) {
                    console.error('Erro ao buscar produtos do Supabase:', error);
                    products = [];
                }
            }

            if (products.length === 0) {
                return interaction.reply({
                    embeds: [Utils.createInfoEmbed('Nenhum produto encontrado. Crie seu primeiro produto!')],
                    flags: 64 // Ephemeral flag
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('🗑️ Selecionar Produto para Excluir')
                .setDescription('⚠️ **ATENÇÃO:** Esta ação não pode ser desfeita!\n\nEscolha qual produto você deseja excluir:')
                .setColor(Utils.getEmbedColor('Danger'));

            const buttons = [];
            products.slice(0, 5).forEach((product, index) => {
                buttons.push(
                    new ButtonBuilder()
                        .setCustomId(`delete_product_${product.id}`)
                        .setLabel(`${index + 1}. ${product.title.substring(0, 50)}`)
                        .setStyle(ButtonStyle.Danger)
                );
            });

            const rows = [];
            for (let i = 0; i < buttons.length; i += 5) {
                const row = new ActionRowBuilder()
                    .addComponents(buttons.slice(i, i + 5));
                rows.push(row);
            }

            await interaction.reply({
                embeds: [embed],
                components: rows,
                flags: 64 // Ephemeral flag
            });

        } catch (error) {
            console.error('Erro ao mostrar seleção de produtos:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar produtos.')],
                flags: 64 // Ephemeral flag
            });
        }
    },

    async showEditOptionsMenu(interaction, database, productId) {
        try {
            console.log(`🔧 Mostrando opções de edição para produto: ${productId}`);
            console.log(`🔧 Interaction type: ${interaction.type}, replied: ${interaction.replied}, deferred: ${interaction.deferred}`);

            // Verificar se a interação ainda é válida
            if (interaction.replied || interaction.deferred) {
                console.warn(`⚠️ Interação já foi respondida ou deferida - ignorando`);
                return;
            }

            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
                console.log(`🔍 Produto do SQLite:`, product);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    console.log(`🔍 Buscando produto no Supabase com ID: ${productId}`);
                    const result = await supabaseLicenseManager.getProductById(productId);
                    console.log(`🔍 Resultado do Supabase:`, result);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link || '',
                            price: parseFloat(result.price)
                        };
                        console.log(`🔍 Produto convertido:`, product);
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                console.error(`❌ Produto não encontrado: ${productId}`);
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64
                });
            }

            console.log(`✅ Produto encontrado: ${product.title}`);

            const embed = new EmbedBuilder()
                .setTitle('<:settings:1399460190335799388> Editar Produto')
                .setDescription(`**${product.title}**\n\nEscolha o que deseja editar:`)
                .addFields(
                    { name: '<:info:1399460563611811870> Versão Atual', value: product.version || 'v1.0.0', inline: true },
                    { name: '<:coin:1399460496213540884> Preço Atual', value: Utils.formatPrice(product.price), inline: true },
                    { name: '<:link:1399460766398021752> Link', value: product.download_link ? '[Ver Link](' + product.download_link + ')' : 'Não definido', inline: true }
                )
                .setColor(Utils.getEmbedColor('Primary'));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`edit_option_${productId}`)
                .setPlaceholder('Selecione o que deseja editar...')
                .addOptions([
                    {
                        label: 'Informações Básicas',
                        description: 'Editar título, descrição e preço',
                        value: 'basic_info',
                        emoji: '📝'
                    },
                    {
                        label: 'Atualizar Plugin',
                        description: 'Nova versão, link e changelog',
                        value: 'update_plugin',
                        emoji: '🔄'
                    },
                    {
                        label: 'Link de Download',
                        description: 'Alterar apenas o link de download',
                        value: 'download_link',
                        emoji: '🔗'
                    },
                    {
                        label: 'Atualizar Mensagens',
                        description: 'Forçar atualização das mensagens publicadas',
                        value: 'update_messages',
                        emoji: '🔄'
                    },
                    {
                        label: 'Configurações',
                        description: 'Canal de changelog e cargo de anúncios',
                        value: 'settings',
                        emoji: '⚙️'
                    },
                    {
                        label: 'Voltar',
                        description: 'Voltar ao menu anterior',
                        value: 'back',
                        emoji: '↩️'
                    }
                ]);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            console.log(`📤 Enviando menu de opções de edição`);
            console.log(`📤 Select menu customId: edit_option_${productId}`);
            await interaction.update({
                embeds: [embed],
                components: [row]
            });
            console.log(`✅ Menu de opções enviado com sucesso`);

        } catch (error) {
            console.error('Erro ao mostrar opções de edição:', error);
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        flags: 64
                    });
                } else {
                    await interaction.update({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        components: []
                    });
                }
            } catch (replyError) {
                console.error('Erro ao responder interação:', replyError);
            }
        }
    },

    async handleEditOption(interaction, database, productId, option) {
        try {
            console.log(`🔧 HandleEditOption chamado: productId=${productId}, option=${option}`);

            switch (option) {
                case 'basic_info':
                    console.log(`📝 Mostrando modal de informações básicas`);
                    await this.showBasicInfoModal(interaction, database, productId);
                    break;
                case 'update_plugin':
                    console.log(`🔄 Mostrando modal de atualização`);
                    await this.showUpdateProductModal(interaction, database, productId);
                    break;
                case 'download_link':
                    console.log(`🔗 Mostrando modal de link de download`);
                    await this.showDownloadLinkModal(interaction, database, productId);
                    break;
                case 'update_messages':
                    console.log(`🔄 Forçando atualização de mensagens`);
                    await this.forceUpdateMessages(interaction, database, productId);
                    break;
                case 'settings':
                    console.log(`⚙️ Mostrando configurações`);
                    await this.showProductSettings(interaction, database, productId);
                    break;
                case 'back':
                    console.log(`↩️ Voltando ao menu de produtos`);
                    await this.showProductsMenu(interaction);
                    break;
                default:
                    console.log(`❌ Opção inválida: ${option}`);
                    await interaction.update({
                        embeds: [Utils.createErrorEmbed('Opção inválida!')],
                        components: []
                    });
            }
        } catch (error) {
            console.error('❌ Erro ao processar opção de edição:', error);
            console.error('❌ Stack trace:', error.stack);

            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        flags: 64
                    });
                } else {
                    await interaction.update({
                        embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                        components: []
                    });
                }
            } catch (replyError) {
                console.error('❌ Erro ao responder interação:', replyError.message);
            }
        }
    },

    // Função para forçar atualização de mensagens (chamada manualmente)
    async forceUpdateMessages(interaction, database, productId) {
        try {
            await interaction.deferUpdate();

            console.log(`🔄 Forçando atualização de mensagens para produto: ${productId}`);

            const updatedCount = await this.updatePublishedProductMessages(interaction.client, database, productId);

            const embed = new EmbedBuilder()
                .setTitle('🔄 Mensagens Atualizadas!')
                .setDescription(updatedCount > 0
                    ? `✅ **${updatedCount} mensagem(s) publicada(s) foram atualizadas com sucesso!**\n\nTodas as mensagens nos canais agora refletem as informações mais recentes do produto.`
                    : `ℹ️ **Nenhuma mensagem publicada encontrada para este produto.**\n\nPara que as mensagens sejam atualizadas automaticamente, você precisa primeiro publicar o produto em um canal.`
                )
                .setColor(updatedCount > 0 ? Utils.getEmbedColor('Success') : Utils.getEmbedColor('Warning'))
                .setTimestamp();

            await interaction.editReply({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('❌ Erro ao forçar atualização de mensagens:', error);
            try {
                await interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Erro ao atualizar mensagens. Tente novamente.')],
                    components: []
                });
            } catch (replyError) {
                console.error('❌ Erro ao responder:', replyError);
            }
        }
    },

    // Função para atualizar mensagens publicadas de um produto
    async updatePublishedProductMessages(client, database, productId) {
        try {
            console.log(`🔄 Atualizando mensagens publicadas do produto: ${productId}`);

            let product = null;
            let publishedMessages = [];

            // Buscar produto
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
                publishedMessages = await database.getPublishedMessages(productId);
            } else {
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price),
                            version: result.version || 'v1.0.0',
                            button_text: 'Comprar Agora',
                            button_color: 'Success',
                            banner_url: null
                        };
                    }
                    publishedMessages = await supabaseLicenseManager.getPublishedMessages(productId);
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                    return;
                }
            }

            if (!product) {
                console.error(`❌ Produto não encontrado: ${productId}`);
                return;
            }

            if (!publishedMessages || publishedMessages.length === 0) {
                console.log(`ℹ️ Nenhuma mensagem publicada encontrada para o produto: ${productId}`);
                return;
            }

            // Criar embed atualizado
            const embed = new EmbedBuilder()
                .setTitle(product.title)
                .setDescription(product.description)
                .addFields(
                    { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(product.price), inline: true },
                    { name: '<:codeterminal:1399897273722212352> Versão', value: product.version || '1.0', inline: true }
                )
                .setColor(Utils.getEmbedColor(product.button_color))
                .setTimestamp();

            if (product.banner_url) {
                embed.setImage(product.banner_url);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`buy_product_${productId}`)
                        .setLabel(product.button_text)
                        .setStyle(Utils.getButtonStyle(product.button_color))
                        .setEmoji('<:cart:1399460437572976820>')
                );

            let updatedCount = 0;

            // Atualizar cada mensagem publicada
            for (const msgData of publishedMessages) {
                try {
                    const channel = client.channels.cache.get(msgData.channel_id);
                    if (!channel) {
                        console.warn(`⚠️ Canal não encontrado: ${msgData.channel_id}`);
                        continue;
                    }

                    const message = await channel.messages.fetch(msgData.message_id);
                    if (!message) {
                        console.warn(`⚠️ Mensagem não encontrada: ${msgData.message_id}`);
                        continue;
                    }

                    await message.edit({
                        embeds: [embed],
                        components: [row]
                    });

                    updatedCount++;
                    console.log(`✅ Mensagem atualizada: ${msgData.message_id} no canal ${channel.name}`);

                } catch (error) {
                    console.error(`❌ Erro ao atualizar mensagem ${msgData.message_id}:`, error);

                    // Se a mensagem foi deletada, remover do banco
                    if (error.code === 10008) { // Unknown Message
                        if (database.isDatabaseAvailable()) {
                            await database.removePublishedMessage(msgData.message_id);
                        } else {
                            const { SupabaseLicenseManager } = require('../supabase');
                            const supabaseLicenseManager = new SupabaseLicenseManager();
                            await supabaseLicenseManager.removePublishedMessage(msgData.message_id);
                        }
                        console.log(`🗑️ Mensagem deletada removida do banco: ${msgData.message_id}`);
                    }
                }
            }

            console.log(`✅ ${updatedCount} mensagens atualizadas para o produto: ${product.title}`);
            return updatedCount;

        } catch (error) {
            console.error('❌ Erro ao atualizar mensagens publicadas:', error);
            return 0;
        }
    },

    // Função para mostrar configurações do produto
    async showProductSettings(interaction, database, productId) {
        try {
            // Buscar configurações atuais
            let productConfig = {};
            try {
                if (database.isDatabaseAvailable()) {
                    productConfig = await database.getProductConfig(productId);
                } else {
                    const { SupabaseLicenseManager } = require('../supabase');
                    const supabaseLicenseManager = new SupabaseLicenseManager();
                    productConfig = await supabaseLicenseManager.getProductConfig(productId);
                }
            } catch (error) {
                console.error('Erro ao buscar configurações:', error);
                productConfig = {};
            }

            const embed = new EmbedBuilder()
                .setTitle('⚙️ Configurações do Produto')
                .setDescription('Configure canal de changelog e cargo para anúncios de atualização:')
                .addFields(
                    {
                        name: '📢 Canal de Changelog',
                        value: productConfig.changelog_channel_id
                            ? `<#${productConfig.changelog_channel_id}>`
                            : '❌ Não configurado',
                        inline: true
                    },
                    {
                        name: '🔔 Cargo de Anúncios',
                        value: productConfig.announcement_role_id
                            ? `<@&${productConfig.announcement_role_id}>`
                            : '❌ Não configurado',
                        inline: true
                    },
                    {
                        name: 'ℹ️ Como Funciona',
                        value: '• **Canal de Changelog:** Onde serão enviadas as atualizações do produto\n• **Cargo de Anúncios:** Cargo que será mencionado nas atualizações',
                        inline: false
                    }
                )
                .setColor(Utils.getEmbedColor('Primary'))
                .setTimestamp();

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`product_settings_${productId}`)
                .setPlaceholder('Selecione o que deseja configurar...')
                .addOptions([
                    {
                        label: 'Definir Canal de Changelog',
                        description: 'Escolher canal para enviar atualizações',
                        value: 'set_changelog_channel',
                        emoji: '📢'
                    },
                    {
                        label: 'Definir Cargo de Anúncios',
                        description: 'Escolher cargo para mencionar nas atualizações',
                        value: 'set_announcement_role',
                        emoji: '🔔'
                    },
                    {
                        label: 'Testar Configurações',
                        description: 'Enviar uma mensagem de teste',
                        value: 'test_settings',
                        emoji: '🧪'
                    },
                    {
                        label: 'Voltar',
                        description: 'Voltar ao menu de edição',
                        value: 'back_to_edit',
                        emoji: '↩️'
                    }
                ]);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('❌ Erro ao mostrar configurações:', error);
            try {
                await interaction.update({
                    embeds: [Utils.createErrorEmbed('Erro ao carregar configurações.')],
                    components: []
                });
            } catch (replyError) {
                console.error('❌ Erro ao responder:', replyError);
            }
        }
    },

    // Handler para configurações do produto
    async handleProductSettings(interaction, database, productId, option) {
        try {
            console.log(`⚙️ HandleProductSettings: productId=${productId}, option=${option}`);

            switch (option) {
                case 'set_changelog_channel':
                    await this.showChannelSelectForChangelog(interaction, productId);
                    break;
                case 'set_announcement_role':
                    await this.showRoleSelectForAnnouncement(interaction, productId);
                    break;
                case 'test_settings':
                    await this.testProductSettings(interaction, database, productId);
                    break;
                case 'back_to_edit':
                    await this.showEditOptionsMenu(interaction, database, productId);
                    break;
                default:
                    await interaction.update({
                        embeds: [Utils.createErrorEmbed('Opção inválida!')],
                        components: []
                    });
            }
        } catch (error) {
            console.error('❌ Erro ao processar configurações:', error);
            try {
                await interaction.update({
                    embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                    components: []
                });
            } catch (replyError) {
                console.error('❌ Erro ao responder:', replyError);
            }
        }
    },

    // Mostrar seleção de canal para changelog
    async showChannelSelectForChangelog(interaction, productId) {
        try {
            const channels = interaction.guild.channels.cache
                .filter(channel => channel.type === 0) // Text channels
                .first(25); // Máximo 25 opções

            if (channels.length === 0) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Nenhum canal de texto encontrado!')],
                    components: []
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('📢 Selecionar Canal de Changelog')
                .setDescription('Escolha o canal onde serão enviadas as atualizações do produto:')
                .setColor(Utils.getEmbedColor('Primary'));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`changelog_channel_${productId}`)
                .setPlaceholder('Selecione um canal...')
                .addOptions(
                    channels.map(channel => ({
                        label: `# ${channel.name}`,
                        description: `Canal: ${channel.name}`,
                        value: channel.id,
                        emoji: '📢'
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('❌ Erro ao mostrar seleção de canal:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao carregar canais.')],
                components: []
            });
        }
    },

    // Mostrar seleção de cargo para anúncios
    async showRoleSelectForAnnouncement(interaction, productId) {
        try {
            const roles = interaction.guild.roles.cache
                .filter(role => !role.managed && role.id !== interaction.guild.id) // Excluir @everyone e bots
                .first(25); // Máximo 25 opções

            if (roles.length === 0) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Nenhum cargo encontrado!')],
                    components: []
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('🔔 Selecionar Cargo de Anúncios')
                .setDescription('Escolha o cargo que será mencionado nas atualizações:')
                .setColor(Utils.getEmbedColor('Primary'));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`announcement_role_${productId}`)
                .setPlaceholder('Selecione um cargo...')
                .addOptions(
                    roles.map(role => ({
                        label: `@${role.name}`,
                        description: `Cargo: ${role.name}`,
                        value: role.id,
                        emoji: '🔔'
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error('❌ Erro ao mostrar seleção de cargo:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao carregar cargos.')],
                components: []
            });
        }
    },

    // Definir canal de changelog
    async setChangelogChannel(interaction, database, productId, channelId) {
        try {
            const channel = interaction.guild.channels.cache.get(channelId);
            if (!channel) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Canal não encontrado!')],
                    components: []
                });
            }

            // Salvar configuração
            await this.saveProductConfig(database, productId, 'changelog_channel_id', channelId);

            const embed = new EmbedBuilder()
                .setTitle('✅ Canal de Changelog Configurado!')
                .setDescription(`Canal **${channel}** foi definido como canal de changelog para este produto.`)
                .addFields(
                    { name: '📢 Canal Configurado', value: `${channel}`, inline: true },
                    { name: 'ℹ️ Próximos Passos', value: 'Agora quando você atualizar o produto, as informações serão enviadas neste canal.', inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`back_to_settings_${productId}`)
                        .setLabel('Voltar às Configurações')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('↩️')
                );

            await interaction.update({
                embeds: [embed],
                components: [backButton]
            });

        } catch (error) {
            console.error('❌ Erro ao definir canal de changelog:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao salvar configuração.')],
                components: []
            });
        }
    },

    // Definir cargo de anúncios
    async setAnnouncementRole(interaction, database, productId, roleId) {
        try {
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Cargo não encontrado!')],
                    components: []
                });
            }

            // Salvar configuração
            await this.saveProductConfig(database, productId, 'announcement_role_id', roleId);

            const embed = new EmbedBuilder()
                .setTitle('✅ Cargo de Anúncios Configurado!')
                .setDescription(`Cargo **${role}** foi definido como cargo de anúncios para este produto.`)
                .addFields(
                    { name: '🔔 Cargo Configurado', value: `${role}`, inline: true },
                    { name: 'ℹ️ Próximos Passos', value: 'Agora quando você atualizar o produto, este cargo será mencionado no changelog.', inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`back_to_settings_${productId}`)
                        .setLabel('Voltar às Configurações')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('↩️')
                );

            await interaction.update({
                embeds: [embed],
                components: [backButton]
            });

        } catch (error) {
            console.error('❌ Erro ao definir cargo de anúncios:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao salvar configuração.')],
                components: []
            });
        }
    },

    // Salvar configuração do produto
    async saveProductConfig(database, productId, key, value) {
        try {
            if (database.isDatabaseAvailable()) {
                // Implementar para SQLite local
                await database.saveProductConfig(productId, key, value);
            } else {
                // Implementar para Supabase
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                await supabaseLicenseManager.saveProductConfig(productId, key, value);
            }
            console.log(`✅ Configuração salva: ${key} = ${value} para produto ${productId}`);
        } catch (error) {
            console.error('❌ Erro ao salvar configuração:', error);
            throw error;
        }
    },

    // Testar configurações do produto
    async testProductSettings(interaction, database, productId) {
        try {
            await interaction.deferUpdate();

            // Buscar produto
            let product = null;
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            // Buscar configurações
            let productConfig = {};
            try {
                if (database.isDatabaseAvailable()) {
                    productConfig = await database.getProductConfig(productId);
                } else {
                    const { SupabaseLicenseManager } = require('../supabase');
                    const supabaseLicenseManager = new SupabaseLicenseManager();
                    productConfig = await supabaseLicenseManager.getProductConfig(productId);
                }
            } catch (error) {
                console.error('Erro ao buscar configurações:', error);
                productConfig = {};
            }

            // Verificar se há configurações
            if (!productConfig.changelog_channel_id && !productConfig.announcement_role_id) {
                const embed = new EmbedBuilder()
                    .setTitle('⚠️ Nenhuma Configuração Encontrada')
                    .setDescription('Configure primeiro o canal de changelog e/ou cargo de anúncios antes de testar.')
                    .setColor(Utils.getEmbedColor('Warning'))
                    .setTimestamp();

                return interaction.editReply({
                    embeds: [embed],
                    components: []
                });
            }

            // Enviar mensagem de teste no canal de changelog
            let testResults = [];

            if (productConfig.changelog_channel_id) {
                try {
                    const changelogChannel = interaction.client.channels.cache.get(productConfig.changelog_channel_id);
                    if (changelogChannel) {
                        const testEmbed = new EmbedBuilder()
                            .setTitle('<:othercrown:1399897102741541044> Teste de Configuração')
                            .setDescription(`**${product.title}** - Teste de changelog`)
                            .addFields(
                                { name: '<:codeservercog:1399849538268696656> Changelog de Teste', value: 'Esta é uma mensagem de teste para verificar se o sistema de changelog está funcionando corretamente.', inline: false },
                                { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(product.price), inline: true },
                                { name: '<:codeterminal:1399897273722212352> Versão', value: product.version, inline: true }
                            )
                            .setColor(Utils.getEmbedColor('Primary'))
                            .setTimestamp();

                        let mentionText = '';
                        if (productConfig.announcement_role_id) {
                            const role = interaction.guild.roles.cache.get(productConfig.announcement_role_id);
                            if (role) {
                                mentionText = `🔔 Teste de menção: ${role} `;
                            }
                        }

                        await changelogChannel.send({
                            content: mentionText + '(Esta é uma mensagem de teste)',
                            embeds: [testEmbed]
                        });

                        testResults.push(`✅ Mensagem de teste enviada em ${changelogChannel}`);

                        if (productConfig.announcement_role_id) {
                            const role = interaction.guild.roles.cache.get(productConfig.announcement_role_id);
                            if (role) {
                                testResults.push(`✅ Cargo ${role} foi mencionado`);
                            } else {
                                testResults.push(`⚠️ Cargo de anúncios não encontrado`);
                            }
                        }
                    } else {
                        testResults.push(`❌ Canal de changelog não encontrado`);
                    }
                } catch (error) {
                    console.error('Erro ao enviar teste:', error);
                    testResults.push(`❌ Erro ao enviar no canal: ${error.message}`);
                }
            }

            // Mostrar resultados
            const embed = new EmbedBuilder()
                .setTitle('🧪 Resultado do Teste')
                .setDescription('Teste das configurações do produto concluído:')
                .addFields(
                    { name: '📊 Resultados', value: testResults.join('\n') || 'Nenhum teste executado', inline: false }
                )
                .setColor(testResults.some(r => r.includes('❌')) ? Utils.getEmbedColor('Warning') : Utils.getEmbedColor('Success'))
                .setTimestamp();

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`back_to_settings_${productId}`)
                        .setLabel('Voltar às Configurações')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('↩️')
                );

            await interaction.editReply({
                embeds: [embed],
                components: [backButton]
            });

        } catch (error) {
            console.error('❌ Erro ao testar configurações:', error);
            try {
                await interaction.editReply({
                    embeds: [Utils.createErrorEmbed('Erro ao executar teste.')],
                    components: []
                });
            } catch (replyError) {
                console.error('❌ Erro ao responder:', replyError);
            }
        }
    },

    async showBasicInfoModal(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link || '',
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            const modal = new ModalBuilder()
                .setCustomId(`basic_info_modal_${productId}`)
                .setTitle('📝 Editar Informações Básicas');

            const titleInput = new TextInputBuilder()
                .setCustomId('product_title')
                .setLabel('Título do Produto')
                .setStyle(TextInputStyle.Short)
                .setValue(product.title)
                .setRequired(true)
                .setMaxLength(100);

            const descriptionInput = new TextInputBuilder()
                .setCustomId('product_description')
                .setLabel('Descrição do Produto')
                .setStyle(TextInputStyle.Paragraph)
                .setValue(product.description)
                .setRequired(true)
                .setMaxLength(1000);

            const priceInput = new TextInputBuilder()
                .setCustomId('product_price')
                .setLabel('Preço (em R$)')
                .setStyle(TextInputStyle.Short)
                .setValue(product.price.toString())
                .setRequired(true);

            const bannerInput = new TextInputBuilder()
                .setCustomId('product_banner')
                .setLabel('URL do Banner (opcional)')
                .setStyle(TextInputStyle.Short)
                .setValue(product.banner_url || '')
                .setRequired(false);

            const firstRow = new ActionRowBuilder().addComponents(titleInput);
            const secondRow = new ActionRowBuilder().addComponents(descriptionInput);
            const thirdRow = new ActionRowBuilder().addComponents(priceInput);
            const fourthRow = new ActionRowBuilder().addComponents(bannerInput);

            modal.addComponents(firstRow, secondRow, thirdRow, fourthRow);

            await interaction.showModal(modal);

        } catch (error) {
            console.error('Erro ao mostrar modal de informações básicas:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                components: []
            });
        }
    },

    async showDownloadLinkModal(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link || '',
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            const modal = new ModalBuilder()
                .setCustomId(`download_link_modal_${productId}`)
                .setTitle('🔗 Alterar Link de Download');

            const downloadLinkInput = new TextInputBuilder()
                .setCustomId('download_link')
                .setLabel('Novo Link de Download')
                .setStyle(TextInputStyle.Short)
                .setValue(product.download_link || '')
                .setPlaceholder('https://cdn.discordapp.com/attachments/...')
                .setRequired(true)
                .setMaxLength(500);

            const reasonInput = new TextInputBuilder()
                .setCustomId('reason')
                .setLabel('Motivo da Alteração (opcional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: Link expirado, nova versão, etc.')
                .setRequired(false)
                .setMaxLength(100);

            const firstRow = new ActionRowBuilder().addComponents(downloadLinkInput);
            const secondRow = new ActionRowBuilder().addComponents(reasonInput);

            modal.addComponents(firstRow, secondRow);

            await interaction.showModal(modal);

        } catch (error) {
            console.error('Erro ao mostrar modal de link de download:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                components: []
            });
        }
    },

    async showUpdateProductModal(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            const modal = new ModalBuilder()
                .setCustomId(`update_product_modal_${productId}`)
                .setTitle('🔄 Atualizar Produto');

            const versionInput = new TextInputBuilder()
                .setCustomId('new_version')
                .setLabel('Nova Versão')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: v1.1.0')
                .setRequired(true)
                .setMaxLength(20);

            const downloadLinkInput = new TextInputBuilder()
                .setCustomId('new_download_link')
                .setLabel('Novo Link de Download')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://cdn.discordapp.com/attachments/...')
                .setValue(product.download_link || '')
                .setRequired(true)
                .setMaxLength(500);

            const changelogInput = new TextInputBuilder()
                .setCustomId('changelog')
                .setLabel('Changelog (uma mudança por linha)')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('- Corrigido bug X\n- Adicionada funcionalidade Y\n- Melhorada performance Z')
                .setRequired(true)
                .setMaxLength(1000);

            const priceInput = new TextInputBuilder()
                .setCustomId('new_price')
                .setLabel('Novo Preço (deixe igual se não mudou)')
                .setStyle(TextInputStyle.Short)
                .setValue(product.price.toString())
                .setRequired(false);

            const notifyUsersInput = new TextInputBuilder()
                .setCustomId('notify_users')
                .setLabel('Notificar compradores? (sim/não)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('sim')
                .setValue('sim')
                .setRequired(false)
                .setMaxLength(3);

            const firstRow = new ActionRowBuilder().addComponents(versionInput);
            const secondRow = new ActionRowBuilder().addComponents(downloadLinkInput);
            const thirdRow = new ActionRowBuilder().addComponents(changelogInput);
            const fourthRow = new ActionRowBuilder().addComponents(priceInput);
            const fifthRow = new ActionRowBuilder().addComponents(notifyUsersInput);

            modal.addComponents(firstRow, secondRow, thirdRow, fourthRow, fifthRow);

            await interaction.showModal(modal);

        } catch (error) {
            console.error('Erro ao mostrar modal de atualização:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                flags: 64
            });
        }
    },

    async showEditProductModal(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price),
                            banner_url: null,
                            button_text: 'Comprar Agora'
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64 // Ephemeral flag
                });
            }

            const modal = new ModalBuilder()
                .setCustomId(`edit_product_modal_${productId}`)
                .setTitle('✏️ Editar Produto');

            const titleInput = new TextInputBuilder()
                .setCustomId('product_title')
                .setLabel('Título do Produto')
                .setStyle(TextInputStyle.Short)
                .setValue(product.title)
                .setRequired(true)
                .setMaxLength(100);

            const descriptionInput = new TextInputBuilder()
                .setCustomId('product_description')
                .setLabel('Descrição do Produto')
                .setStyle(TextInputStyle.Paragraph)
                .setValue(product.description)
                .setRequired(true)
                .setMaxLength(1000);

            const priceInput = new TextInputBuilder()
                .setCustomId('product_price')
                .setLabel('Preço (em R$)')
                .setStyle(TextInputStyle.Short)
                .setValue(product.price.toString())
                .setRequired(true);

            const bannerInput = new TextInputBuilder()
                .setCustomId('product_banner')
                .setLabel('URL do Banner (opcional)')
                .setStyle(TextInputStyle.Short)
                .setValue(product.banner_url || '')
                .setRequired(false);

            const buttonTextInput = new TextInputBuilder()
                .setCustomId('button_text')
                .setLabel('Texto do Botão')
                .setStyle(TextInputStyle.Short)
                .setValue(product.button_text)
                .setRequired(false)
                .setMaxLength(80);

            const firstRow = new ActionRowBuilder().addComponents(titleInput);
            const secondRow = new ActionRowBuilder().addComponents(descriptionInput);
            const thirdRow = new ActionRowBuilder().addComponents(priceInput);
            const fourthRow = new ActionRowBuilder().addComponents(bannerInput);
            const fifthRow = new ActionRowBuilder().addComponents(buttonTextInput);

            modal.addComponents(firstRow, secondRow, thirdRow, fourthRow, fifthRow);

            await interaction.showModal(modal);

        } catch (error) {
            console.error('Erro ao mostrar modal de edição:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar produto para edição.')],
                flags: 64 // Ephemeral flag
            });
        }
    },

    async confirmDeleteProduct(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64 // Ephemeral flag
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('⚠️ Confirmar Exclusão')
                .setDescription(`Tem certeza que deseja excluir o produto **${product.title}**?\n\n⚠️ **Esta ação não pode ser desfeita!**`)
                .addFields(
                    { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(product.price), inline: true },
                    { name: '🆔 ID', value: `\`${product.id}\``, inline: true }
                )
                .setColor(Utils.getEmbedColor('Danger'))
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`confirm_delete_${productId}`)
                        .setLabel('🗑️ Sim, Excluir')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('cancel_delete')
                        .setLabel('❌ Cancelar')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row],
                flags: 64 // Ephemeral flag
            });

        } catch (error) {
            console.error('Erro ao confirmar exclusão:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro ao carregar produto.')],
                flags: 64 // Ephemeral flag
            });
        }
    },

    async deleteProduct(interaction, database, productId) {
        try {
            let product = null;

            // Buscar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        // Converter formato Supabase para formato esperado
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.update({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    components: []
                });
            }

            // Deletar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.deleteProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                await supabaseLicenseManager.deleteProduct(productId);
                console.log('✅ Produto deletado do Supabase');
            }

            const embed = new EmbedBuilder()
                .setTitle('✅ Produto Excluído')
                .setDescription(`O produto **${product.title}** foi excluído com sucesso.`)
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.update({
                embeds: [embed],
                components: []
            });

        } catch (error) {
            console.error('Erro ao excluir produto:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao excluir produto.')],
                components: []
            });
        }
    },

    async cancelDelete(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('❌ Exclusão Cancelada')
            .setDescription('A exclusão do produto foi cancelada.')
            .setColor(Utils.getEmbedColor('Secondary'))
            .setTimestamp();

        await interaction.update({
            embeds: [embed],
            components: []
        });
    },

    async handleBasicInfoModal(interaction, database) {
        try {
            const productId = interaction.customId.replace('basic_info_modal_', '');
            const title = interaction.fields.getTextInputValue('product_title');
            const description = interaction.fields.getTextInputValue('product_description');
            const priceStr = interaction.fields.getTextInputValue('product_price');
            const banner = interaction.fields.getTextInputValue('product_banner') || null;

            // Validar preço
            const price = parseFloat(priceStr.replace(',', '.'));
            if (isNaN(price) || price <= 0) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Preço inválido! Use apenas números (ex: 49.90)')],
                    flags: 64
                });
            }

            // Atualizar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.updateProduct(productId, {
                    title: Utils.sanitizeInput(title),
                    description: Utils.sanitizeInput(description),
                    price: price,
                    banner_url: banner
                });
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    await supabaseLicenseManager.updateProduct(productId, {
                        name: Utils.sanitizeInput(title),
                        description: Utils.sanitizeInput(description),
                        price: price,
                        banner_url: banner
                    });
                } catch (error) {
                    console.error('Erro ao atualizar produto no Supabase:', error);
                    throw error;
                }
            }

            // Atualizar mensagens publicadas automaticamente
            let updatedMessages = 0;
            try {
                updatedMessages = await this.updatePublishedProductMessages(interaction.client, database, productId);
            } catch (updateError) {
                console.error('❌ Erro ao atualizar mensagens publicadas:', updateError);
            }

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Informações Atualizadas!')
                .setDescription(`**${title}** foi atualizado com sucesso!${updatedMessages > 0 ? `\n\n🔄 **${updatedMessages} mensagem(s) publicada(s) atualizada(s) automaticamente!**` : ''}`)
                .addFields(
                    { name: '<:coin:1399460496213540884> Novo Preço', value: Utils.formatPrice(price), inline: true }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.reply({
                embeds: [embed],
                flags: 64
            });

        } catch (error) {
            console.error('Erro ao atualizar informações básicas:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno ao atualizar produto.')],
                flags: 64
            });
        }
    },

    async handleDownloadLinkModal(interaction, database) {
        try {
            const productId = interaction.customId.replace('download_link_modal_', '');
            const downloadLink = interaction.fields.getTextInputValue('download_link');
            const reason = interaction.fields.getTextInputValue('reason') || 'Link atualizado';

            // Validar link
            if (!downloadLink.startsWith('http://') && !downloadLink.startsWith('https://')) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Link deve começar com http:// ou https://')],
                    flags: 64
                });
            }

            // Buscar produto atual (SQLite local ou Supabase)
            let currentProduct = null;
            if (database.isDatabaseAvailable()) {
                currentProduct = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        currentProduct = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!currentProduct) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64
                });
            }

            // Atualizar link (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.updateProduct(productId, {
                    download_link: downloadLink
                });
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    await supabaseLicenseManager.updateProduct(productId, {
                        download_link: downloadLink
                    });
                } catch (error) {
                    console.error('Erro ao atualizar produto no Supabase:', error);
                    throw error;
                }
            }

            // Atualizar mensagens publicadas automaticamente
            let updatedMessages = 0;
            try {
                updatedMessages = await this.updatePublishedProductMessages(interaction.client, database, productId);
            } catch (updateError) {
                console.error('❌ Erro ao atualizar mensagens publicadas:', updateError);
            }

            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Link Atualizado!')
                .setDescription(`Link de download do **${currentProduct.title}** foi atualizado!${updatedMessages > 0 ? `\n\n🔄 **${updatedMessages} mensagem(s) publicada(s) atualizada(s) automaticamente!**` : ''}`)
                .addFields(
                    { name: '<:link:1399460766398021752> Novo Link', value: `[Clique aqui](${downloadLink})`, inline: false },
                    { name: '<:info:1399460563611811870> Motivo', value: reason, inline: false }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.reply({
                embeds: [embed],
                flags: 64
            });

            console.log(`🔗 Link de download atualizado para produto ${currentProduct.title}: ${downloadLink}`);

        } catch (error) {
            console.error('Erro ao atualizar link de download:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno ao atualizar link.')],
                flags: 64
            });
        }
    },

    async handleUpdateProductModal(interaction, database) {
        try {
            const productId = interaction.customId.replace('update_product_modal_', '');
            const newVersion = interaction.fields.getTextInputValue('new_version');
            const newDownloadLink = interaction.fields.getTextInputValue('new_download_link');
            const changelog = interaction.fields.getTextInputValue('changelog');
            const newPriceStr = interaction.fields.getTextInputValue('new_price');
            const notifyUsers = interaction.fields.getTextInputValue('notify_users').toLowerCase();

            // Buscar produto atual (SQLite local ou Supabase)
            let currentProduct = null;
            if (database.isDatabaseAvailable()) {
                currentProduct = await database.getProduct(productId);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    const result = await supabaseLicenseManager.getProductById(productId);
                    if (result) {
                        currentProduct = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            version: result.version || 'v1.0.0',
                            download_link: result.download_link,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!currentProduct) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64
                });
            }

            // Validar link de download
            if (!newDownloadLink.startsWith('http://') && !newDownloadLink.startsWith('https://')) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Link de download deve começar com http:// ou https://')],
                    flags: 64
                });
            }

            // Preparar dados de atualização
            const updateData = {
                version: newVersion,
                download_link: newDownloadLink
            };

            // Atualizar preço se fornecido
            if (newPriceStr && newPriceStr.trim() !== '') {
                const newPrice = parseFloat(newPriceStr.replace(',', '.'));
                if (!isNaN(newPrice) && newPrice > 0) {
                    updateData.price = newPrice;
                }
            }

            // Atualizar produto no banco (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.updateProduct(productId, updateData);
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const { SupabaseLicenseManager } = require('../supabase');
                const supabaseLicenseManager = new SupabaseLicenseManager();
                try {
                    // Converter campos para formato Supabase
                    const supabaseUpdateData = {};
                    if (updateData.version) supabaseUpdateData.version = updateData.version;
                    if (updateData.download_link) supabaseUpdateData.download_link = updateData.download_link;
                    if (updateData.price) supabaseUpdateData.price = updateData.price;

                    await supabaseLicenseManager.updateProduct(productId, supabaseUpdateData);
                } catch (error) {
                    console.error('Erro ao atualizar produto no Supabase:', error);
                    throw error;
                }
            }

            // Atualizar mensagens publicadas automaticamente
            let updatedMessages = 0;
            try {
                updatedMessages = await this.updatePublishedProductMessages(interaction.client, database, productId);
            } catch (updateError) {
                console.error('❌ Erro ao atualizar mensagens publicadas:', updateError);
            }

            // Resposta de sucesso
            const embed = new EmbedBuilder()
                .setTitle('<:success:1399460535518363658> Produto Atualizado!')
                .setDescription(`**${currentProduct.title}** foi atualizado com sucesso!${updatedMessages > 0 ? `\n\n🔄 **${updatedMessages} mensagem(s) publicada(s) atualizada(s) automaticamente!**` : ''}`)
                .addFields(
                    { name: '<:info:1399460563611811870> Nova Versão', value: newVersion, inline: true },
                    { name: '<:link:1399460766398021752> Link Atualizado', value: '[Clique aqui](' + newDownloadLink + ')', inline: true },
                    { name: '<:settings:1399460190335799388> Notificar Usuários', value: notifyUsers === 'sim' ? 'Sim' : 'Não', inline: true }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.reply({
                embeds: [embed],
                flags: 64
            });

            // Notificar compradores se solicitado
            if (notifyUsers === 'sim') {
                await this.notifyProductUpdate(currentProduct, newVersion, changelog, newDownloadLink, database, interaction.client);
            }

        } catch (error) {
            console.error('Erro ao atualizar produto:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno ao atualizar produto.')],
                flags: 64
            });
        }
    },

    async notifyProductUpdate(product, newVersion, changelog, downloadLink, database, client) {
        try {
            console.log(`📢 Iniciando notificação de atualização para produto: ${product.title}`);

            // Buscar configurações do produto
            let productConfig = {};
            try {
                if (database.isDatabaseAvailable()) {
                    productConfig = await database.getProductConfig(product.id);
                } else {
                    const { SupabaseLicenseManager } = require('../supabase');
                    const supabaseLicenseManager = new SupabaseLicenseManager();
                    productConfig = await supabaseLicenseManager.getProductConfig(product.id);
                }
            } catch (error) {
                console.error('Erro ao buscar configurações do produto:', error);
                productConfig = {};
            }

            // Enviar changelog no canal configurado
            if (productConfig.changelog_channel_id) {
                try {
                    const changelogChannel = client.channels.cache.get(productConfig.changelog_channel_id);
                    if (changelogChannel) {
                        const changelogEmbed = new EmbedBuilder()
                            .setTitle('<:codewrench:1399842078502031410> Atualização de Produto!')
                            .setDescription(`**${product.title}** foi atualizado para a versão **${newVersion}**`)
                            .addFields(
                                { name: '<:codeservercog:1399849538268696656> Changelog', value: changelog, inline: false },
                                { name: '<:actioninfo:1399849331703550135> Nova Versão', value: newVersion, inline: true }
                            )
                            .setColor(Utils.getEmbedColor('Success'))
                            .setTimestamp();

                        const downloadButton = new ActionRowBuilder()
                            .addComponents(
                                new ButtonBuilder()
                                    .setCustomId(`buy_product_${product.id}`)
                                    .setLabel('Mensagem Do Sistema')
                                    .setStyle(ButtonStyle.Secondary)
                                    .setDisabled(true)
                            );

                        let mentionText = '';
                        if (productConfig.announcement_role_id) {
                            mentionText = `<@&${productConfig.announcement_role_id}> `;
                        }

                        await changelogChannel.send({
                            content: mentionText,
                            embeds: [changelogEmbed],
                            components: [downloadButton]
                        });

                        console.log(`📢 Changelog enviado no canal: ${changelogChannel.name}`);
                    } else {
                        console.warn(`⚠️ Canal de changelog não encontrado: ${productConfig.changelog_channel_id}`);
                    }
                } catch (error) {
                    console.error('❌ Erro ao enviar changelog:', error);
                }
            }

            // Buscar todos os compradores do produto
            let sales = [];
            if (database.isDatabaseAvailable()) {
                sales = await database.query('SELECT DISTINCT user_id, username FROM sales WHERE product_id = ? AND status = "approved"', [product.id]);
            }

            if (sales.length === 0) {
                console.log('📢 Nenhum comprador encontrado para notificar via DM');
                return;
            }

            console.log(`📢 Encontrados ${sales.length} compradores para notificar via DM`);

            // Criar embed de notificação
            const updateEmbed = new EmbedBuilder()
                .setTitle('<:update:1399460575117053952> Plugin Atualizado!')
                .setDescription(`**${product.title}** foi atualizado para a versão **${newVersion}**`)
                .addFields(
                    { name: '<:changelog:1399460563611811870> Changelog', value: changelog, inline: false },
                    { name: '<:info:1399460563611811870> Como Atualizar', value: '1. Baixe a nova versão usando o botão abaixo\n2. Substitua o arquivo antigo\n3. Reinicie seu servidor', inline: false }
                )
                .setColor(Utils.getEmbedColor('Warning'))
                .setTimestamp();

            const downloadButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('📥 Baixar Nova Versão')
                        .setStyle(ButtonStyle.Link)
                        .setURL(downloadLink),
                    new ButtonBuilder()
                        .setLabel('🎧 Suporte')
                        .setStyle(ButtonStyle.Secondary)
                        .setCustomId('support_button')
                );

            // Enviar notificação para cada comprador
            let notifiedCount = 0;
            for (const sale of sales) {
                try {
                    const user = await client.users.fetch(sale.user_id);
                    await user.send({
                        embeds: [updateEmbed],
                        components: [downloadButton]
                    });
                    notifiedCount++;
                    console.log(`📢 Notificação enviada para: ${sale.username}`);
                } catch (error) {
                    console.error(`❌ Erro ao notificar usuário ${sale.username}:`, error.message);
                }
            }

            console.log(`📢 Notificação concluída: ${notifiedCount}/${sales.length} usuários notificados`);

        } catch (error) {
            console.error('❌ Erro ao notificar compradores:', error);
        }
    },

    async handleEditProductModal(interaction, database) {
        const productId = interaction.customId.replace('edit_product_modal_', '');
        const title = interaction.fields.getTextInputValue('product_title');
        const description = interaction.fields.getTextInputValue('product_description');
        const priceStr = interaction.fields.getTextInputValue('product_price');
        const banner = interaction.fields.getTextInputValue('product_banner') || null;
        const buttonText = interaction.fields.getTextInputValue('button_text') || 'Comprar Agora';

        // Validar preço
        const price = parseFloat(priceStr.replace(',', '.'));
        if (isNaN(price) || price <= 0) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed('Preço inválido! Use apenas números (ex: 49.90)')],
                flags: 64 // Ephemeral flag
            });
        }

        // Validar dados
        const validationErrors = Utils.validateProductData({
            title,
            description,
            price,
            banner_url: banner
        });

        if (validationErrors.length > 0) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed(validationErrors.join('\n'))],
                flags: 64 // Ephemeral flag
            });
        }

        try {
            // Atualizar produto (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.updateProduct(productId, {
                    title: Utils.sanitizeInput(title),
                    description: Utils.sanitizeInput(description),
                    banner_url: banner,
                    button_text: Utils.sanitizeInput(buttonText),
                    price: price
                });
            } else {
                // Usar Supabase quando SQLite não estiver disponível
                const supabaseLicenseManager = new SupabaseLicenseManager();
                await supabaseLicenseManager.updateProduct(productId, {
                    title: Utils.sanitizeInput(title),
                    description: Utils.sanitizeInput(description),
                    price: price
                });
                console.log('✅ Produto atualizado no Supabase');
            }

            const embed = new EmbedBuilder()
                .setTitle('✅ Produto Atualizado!')
                .setDescription(`**${title}** foi atualizado com sucesso.`)
                .addFields(
                    { name: '<:coin:1399460496213540884> Preço', value: Utils.formatPrice(price), inline: true },
                    { name: '🆔 ID', value: `\`${productId}\``, inline: true }
                )
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            await interaction.reply({
                embeds: [embed],
                flags: 64 // Ephemeral flag
            });

        } catch (error) {
            console.error('Erro ao atualizar produto:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno ao atualizar produto. Tente novamente.')],
                flags: 64 // Ephemeral flag
            });
        }
    }
};