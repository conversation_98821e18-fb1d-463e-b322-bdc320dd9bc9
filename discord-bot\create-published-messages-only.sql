-- C<PERSON>r apenas a tabela published_messages (product_configs já existe)
-- Execute no SQL Editor do Supabase

CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>r índices para performance
CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_channel_id ON published_messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_guild_id ON published_messages(guild_id);

-- Verificar se a tabela foi criada
SELECT 'Tabela published_messages criada com sucesso!' as status;

-- Verificar estrutura
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'published_messages' 
ORDER BY ordinal_position;