package com.stoneplugins.core;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.server.ServerLoadEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * StonePlugins Core - Plugin central de autenticação e gerenciamento
 * Este plugin é obrigatório para todos os outros plugins da StonePlugins funcionarem
 * 
 * Funcionalidades:
 * - Autenticação de licenças
 * - Verificação de integridade dos plugins
 * - Sistema anti-pirataria
 * - Atualizações automáticas
 * - Monitoramento de uso
 */
public class StonePluginsCore extends JavaPlugin implements Listener {
    
    // Configurações da API
    private static final String API_BASE_URL = "https://discord-stone-plugins-1.onrender.com/api";
    private static final String LICENSE_ENDPOINT = "/license/validate";
    private static final String UPDATE_ENDPOINT = "/license/update-check";
    private static final String ACTIVATION_ENDPOINT = "/license/activate";
    
    // Dados do servidor
    private String serverIP;
    private int serverPort;
    private String serverUUID;
    
    // Licenças ativas
    private Map<String, LicenseData> activeLicenses = new ConcurrentHashMap<>();
    private Map<String, PluginInfo> registeredPlugins = new ConcurrentHashMap<>();
    
    // Status do sistema
    private boolean systemInitialized = false;
    private boolean apiConnected = false;
    
    @Override
    public void onEnable() {
        // Salvar configuração padrão
        saveDefaultConfig();
        
        // Registrar eventos
        getServer().getPluginManager().registerEvents(this, this);
        
        // Inicializar sistema
        initializeSystem();
        
        // Registrar comandos
        getCommand("stoneplugins").setExecutor(this);
        
        // Iniciar verificações periódicas
        startPeriodicChecks();
        
        getLogger().info("§a[StonePlugins Core] Plugin inicializado com sucesso!");
        getLogger().info("§e[StonePlugins Core] Versão: " + getDescription().getVersion());
    }
    
    @Override
    public void onDisable() {
        // Notificar API sobre desconexão
        notifyServerShutdown();
        
        getLogger().info("§c[StonePlugins Core] Plugin desabilitado!");
    }
    
    /**
     * Inicializar sistema de autenticação
     */
    private void initializeSystem() {
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // Obter dados do servidor
                    collectServerData();
                    
                    // Conectar com API
                    connectToAPI();
                    
                    // Carregar licenças salvas
                    loadSavedLicenses();
                    
                    // Verificar plugins dependentes
                    checkDependentPlugins();
                    
                    systemInitialized = true;
                    
                    getLogger().info("§a[StonePlugins Core] Sistema inicializado com sucesso!");
                    
                } catch (Exception e) {
                    getLogger().severe("§c[StonePlugins Core] Erro ao inicializar sistema: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }.runTaskAsynchronously(this);
    }
    
    /**
     * Coletar dados do servidor para identificação única
     */
    private void collectServerData() {
        try {
            // IP do servidor
            serverIP = getServer().getIp();
            if (serverIP == null || serverIP.isEmpty()) {
                serverIP = "localhost";
            }
            
            // Porta do servidor
            serverPort = getServer().getPort();
            
            // UUID único do servidor (baseado em dados únicos)
            String uniqueData = serverIP + ":" + serverPort + ":" + 
                               getDataFolder().getAbsolutePath() + ":" +
                               System.getProperty("user.name");
            serverUUID = UUID.nameUUIDFromBytes(uniqueData.getBytes()).toString();
            
            getLogger().info("§e[StonePlugins Core] Servidor identificado: " + serverUUID);
            
        } catch (Exception e) {
            getLogger().warning("§c[StonePlugins Core] Erro ao coletar dados do servidor: " + e.getMessage());
        }
    }
    
    /**
     * Conectar com a API de licenças
     */
    private void connectToAPI() {
        try {
            URL url = new URL(API_BASE_URL + "/health");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                apiConnected = true;
                getLogger().info("§a[StonePlugins Core] Conectado à API com sucesso!");
            } else {
                getLogger().warning("§c[StonePlugins Core] API retornou código: " + responseCode);
            }
            
        } catch (Exception e) {
            getLogger().warning("§c[StonePlugins Core] Erro ao conectar com API: " + e.getMessage());
            apiConnected = false;
        }
    }
    
    /**
     * Processar comandos
     */
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!command.getName().equalsIgnoreCase("stoneplugins")) {
            return false;
        }
        
        if (args.length == 0) {
            showHelp(sender);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "ativar":
                if (args.length < 2) {
                    sender.sendMessage("§c[StonePlugins] Uso: /stoneplugins ativar <licença>");
                    return true;
                }
                activateLicense(sender, args[1]);
                break;
                
            case "status":
                showStatus(sender);
                break;
                
            case "licencas":
                showLicenses(sender);
                break;
                
            case "plugins":
                showRegisteredPlugins(sender);
                break;
                
            case "reload":
                if (!sender.hasPermission("stoneplugins.admin")) {
                    sender.sendMessage("§c[StonePlugins] Você não tem permissão para isso!");
                    return true;
                }
                reloadSystem(sender);
                break;
                
            default:
                showHelp(sender);
                break;
        }
        
        return true;
    }
    
    /**
     * Ativar uma licença
     */
    private void activateLicense(CommandSender sender, String licenseKey) {
        if (!apiConnected) {
            sender.sendMessage("§c[StonePlugins] Erro: Não foi possível conectar com o servidor de licenças!");
            return;
        }
        
        sender.sendMessage("§e[StonePlugins] Verificando licença...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // Fazer requisição para ativar licença
                    String response = activateLicenseAPI(licenseKey);
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            processLicenseActivation(sender, licenseKey, response);
                        }
                    }.runTask(StonePluginsCore.this);
                    
                } catch (Exception e) {
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage("§c[StonePlugins] Erro ao ativar licença: " + e.getMessage());
                        }
                    }.runTask(StonePluginsCore.this);
                }
            }
        }.runTaskAsynchronously(this);
    }
    
    /**
     * Fazer requisição para ativar licença na API
     */
    private String activateLicenseAPI(String licenseKey) throws Exception {
        URL url = new URL(API_BASE_URL + ACTIVATION_ENDPOINT);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // Dados para enviar
        String jsonData = String.format(
            "{\"license_key\":\"%s\",\"server_ip\":\"%s\",\"server_port\":%d,\"server_uuid\":\"%s\"}",
            licenseKey, serverIP, serverPort, serverUUID
        );
        
        // Enviar dados
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // Ler resposta
        int responseCode = connection.getResponseCode();
        InputStream inputStream = responseCode >= 200 && responseCode < 300 
            ? connection.getInputStream() 
            : connection.getErrorStream();
            
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        if (responseCode != 200) {
            throw new Exception("API retornou código " + responseCode + ": " + response.toString());
        }
        
        return response.toString();
    }
    
    /**
     * Processar resultado da ativação de licença
     */
    private void processLicenseActivation(CommandSender sender, String licenseKey, String response) {
        try {
            // Parse simples da resposta JSON (você pode usar uma biblioteca JSON se preferir)
            if (response.contains("\"success\":true")) {
                // Extrair dados da licença da resposta
                String productName = extractJsonValue(response, "product_name");
                String expiresAt = extractJsonValue(response, "expires_at");
                
                // Salvar licença
                LicenseData licenseData = new LicenseData(licenseKey, productName, expiresAt, serverUUID);
                activeLicenses.put(productName, licenseData);
                saveLicenseToFile(licenseData);
                
                // Notificar sucesso
                sender.sendMessage("§a[StonePlugins] ✅ Licença ativada com sucesso!");
                sender.sendMessage("§a[StonePlugins] 📦 Produto: §f" + productName);
                if (expiresAt != null && !expiresAt.equals("null")) {
                    sender.sendMessage("§a[StonePlugins] ⏰ Expira em: §f" + expiresAt);
                } else {
                    sender.sendMessage("§a[StonePlugins] ⏰ Licença: §fVitalícia");
                }
                
                // Verificar se há plugins dependentes para ativar
                checkAndActivateDependentPlugins(productName);
                
            } else {
                String error = extractJsonValue(response, "error");
                sender.sendMessage("§c[StonePlugins] ❌ Erro ao ativar licença: " + error);
            }
            
        } catch (Exception e) {
            sender.sendMessage("§c[StonePlugins] ❌ Erro ao processar resposta: " + e.getMessage());
        }
    }
    
    /**
     * Extrair valor de JSON simples (método básico)
     */
    private String extractJsonValue(String json, String key) {
        String searchKey = "\"" + key + "\":\"";
        int startIndex = json.indexOf(searchKey);
        if (startIndex == -1) {
            return null;
        }
        startIndex += searchKey.length();
        int endIndex = json.indexOf("\"", startIndex);
        if (endIndex == -1) {
            return null;
        }
        return json.substring(startIndex, endIndex);
    }
    
    /**
     * Mostrar ajuda dos comandos
     */
    private void showHelp(CommandSender sender) {
        sender.sendMessage("§6§l[StonePlugins Core] §fComandos disponíveis:");
        sender.sendMessage("§e/stoneplugins ativar <licença> §7- Ativar uma licença");
        sender.sendMessage("§e/stoneplugins status §7- Ver status do sistema");
        sender.sendMessage("§e/stoneplugins licencas §7- Ver licenças ativas");
        sender.sendMessage("§e/stoneplugins plugins §7- Ver plugins registrados");
        if (sender.hasPermission("stoneplugins.admin")) {
            sender.sendMessage("§e/stoneplugins reload §7- Recarregar sistema");
        }
    }
    
    /**
     * Mostrar status do sistema
     */
    private void showStatus(CommandSender sender) {
        sender.sendMessage("§6§l[StonePlugins Core] §fStatus do Sistema:");
        sender.sendMessage("§7Versão: §f" + getDescription().getVersion());
        sender.sendMessage("§7API: " + (apiConnected ? "§aConectada" : "§cDesconectada"));
        sender.sendMessage("§7Servidor UUID: §f" + serverUUID);
        sender.sendMessage("§7Licenças Ativas: §f" + activeLicenses.size());
        sender.sendMessage("§7Plugins Registrados: §f" + registeredPlugins.size());
    }
    
    /**
     * Mostrar licenças ativas
     */
    private void showLicenses(CommandSender sender) {
        sender.sendMessage("§6§l[StonePlugins Core] §fLicenças Ativas:");
        
        if (activeLicenses.isEmpty()) {
            sender.sendMessage("§7Nenhuma licença ativa encontrada.");
            sender.sendMessage("§7Use §e/stoneplugins ativar <licença> §7para ativar uma licença.");
            return;
        }
        
        for (LicenseData license : activeLicenses.values()) {
            sender.sendMessage("§a📦 " + license.getProductName());
            sender.sendMessage("§7  Licença: §f" + license.getLicenseKey().substring(0, 8) + "...");
            if (license.getExpiresAt() != null) {
                sender.sendMessage("§7  Expira: §f" + license.getExpiresAt());
            } else {
                sender.sendMessage("§7  Tipo: §aVitalícia");
            }
        }
    }
    
    /**
     * Mostrar plugins registrados
     */
    private void showRegisteredPlugins(CommandSender sender) {
        sender.sendMessage("§6§l[StonePlugins Core] §fPlugins Registrados:");
        
        if (registeredPlugins.isEmpty()) {
            sender.sendMessage("§7Nenhum plugin StonePlugins encontrado.");
            return;
        }
        
        for (PluginInfo plugin : registeredPlugins.values()) {
            String status = plugin.isActive() ? "§aAtivo" : "§cInativo";
            sender.sendMessage("§e📦 " + plugin.getPluginName() + " §7v" + plugin.getVersion());
            sender.sendMessage("§7  Status: " + status);
            sender.sendMessage("§7  Licença: §f" + plugin.getRequiredLicense());
        }
    }
    
    /**
     * Recarregar sistema
     */
    private void reloadSystem(CommandSender sender) {
        sender.sendMessage("§e[StonePlugins] Recarregando sistema...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // Reconectar com API
                    connectToAPI();
                    
                    // Recarregar licenças
                    loadSavedLicenses();
                    
                    // Verificar plugins
                    checkDependentPlugins();
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage("§a[StonePlugins] Sistema recarregado com sucesso!");
                        }
                    }.runTask(StonePluginsCore.this);
                    
                } catch (Exception e) {
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage("§c[StonePlugins] Erro ao recarregar: " + e.getMessage());
                        }
                    }.runTask(StonePluginsCore.this);
                }
            }
        }.runTaskAsynchronously(this);
    }
    
    /**
     * Carregar licenças salvas do arquivo
     */
    private void loadSavedLicenses() {
        File licensesFile = new File(getDataFolder(), "licenses.dat");
        if (!licensesFile.exists()) {
            return;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(licensesFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                LicenseData license = LicenseData.fromFileString(line);
                if (license != null && !license.isExpired()) {
                    activeLicenses.put(license.getProductName(), license);
                }
            }
            
            getLogger().info("§a[StonePlugins Core] Carregadas " + activeLicenses.size() + " licenças salvas");
            
        } catch (Exception e) {
            getLogger().warning("§c[StonePlugins Core] Erro ao carregar licenças: " + e.getMessage());
        }
    }
    
    /**
     * Salvar licença em arquivo
     */
    private void saveLicenseToFile(LicenseData license) {
        try {
            File licensesFile = new File(getDataFolder(), "licenses.dat");
            getDataFolder().mkdirs();
            
            // Ler licenças existentes
            List<String> existingLicenses = new ArrayList<>();
            if (licensesFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(licensesFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // Não adicionar se for a mesma licença (atualização)
                        if (!line.startsWith(license.getLicenseKey() + "|")) {
                            existingLicenses.add(line);
                        }
                    }
                }
            }
            
            // Adicionar nova licença
            existingLicenses.add(license.toFileString());
            
            // Salvar todas as licenças
            try (PrintWriter writer = new PrintWriter(new FileWriter(licensesFile))) {
                for (String licenseString : existingLicenses) {
                    writer.println(licenseString);
                }
            }
            
        } catch (Exception e) {
            getLogger().warning("§c[StonePlugins Core] Erro ao salvar licença: " + e.getMessage());
        }
    }
    
    /**
     * Verificar plugins dependentes
     */
    private void checkDependentPlugins() {
        // Verificar todos os plugins carregados
        for (org.bukkit.plugin.Plugin plugin : getServer().getPluginManager().getPlugins()) {
            if (isStonePlugin(plugin)) {
                String requiredLicense = getRequiredLicense(plugin);
                if (requiredLicense != null) {
                    PluginInfo pluginInfo = new PluginInfo(
                        plugin.getName(), 
                        plugin.getDescription().getVersion(), 
                        requiredLicense
                    );
                    
                    // Verificar se tem licença ativa
                    boolean hasLicense = activeLicenses.containsKey(requiredLicense);
                    pluginInfo.setActive(hasLicense);
                    
                    registeredPlugins.put(plugin.getName(), pluginInfo);
                    
                    if (!hasLicense) {
                        getLogger().warning("§c[StonePlugins Core] Plugin " + plugin.getName() + 
                                          " requer licença: " + requiredLicense);
                    }
                }
            }
        }
    }
    
    /**
     * Verificar se é um plugin da StonePlugins
     */
    private boolean isStonePlugin(org.bukkit.plugin.Plugin plugin) {
        // Plugins que não precisam de licença
        if (plugin.getName().equals("StonePlugins-Core") || 
            plugin.getName().equals("StonePluginsDepend") ||
            plugin.getName().startsWith("StonePlugins-Core") ||
            plugin.getName().startsWith("StonePluginsDepend")) {
            return false;
        }
        
        // Verificar se o plugin tem a dependência do StonePlugins Core
        List<String> dependencies = plugin.getDescription().getDepend();
        List<String> softDependencies = plugin.getDescription().getSoftDepend();
        
        return (dependencies != null && dependencies.contains("StonePlugins-Core")) ||
               (softDependencies != null && softDependencies.contains("StonePlugins-Core")) ||
               plugin.getDescription().getAuthors().contains("StonePlugins") ||
               plugin.getDescription().getMain().startsWith("com.stoneplugins.");
    }
    
    /**
     * Obter licença requerida pelo plugin
     */
    private String getRequiredLicense(org.bukkit.plugin.Plugin plugin) {
        // Fallback: usar nome do plugin como licença requerida
        return plugin.getName();
    }
    
    /**
     * Verificar e ativar plugins dependentes
     */
    private void checkAndActivateDependentPlugins(String productName) {
        for (PluginInfo pluginInfo : registeredPlugins.values()) {
            if (pluginInfo.getRequiredLicense().equals(productName)) {
                pluginInfo.setActive(true);
                getLogger().info("§a[StonePlugins Core] Plugin " + pluginInfo.getPluginName() + " ativado!");
                
                // Notificar o plugin que foi ativado
                notifyPluginActivation(pluginInfo.getPluginName());
            }
        }
    }
    
    /**
     * Notificar plugin sobre ativação
     */
    private void notifyPluginActivation(String pluginName) {
        org.bukkit.plugin.Plugin plugin = getServer().getPluginManager().getPlugin(pluginName);
        if (plugin != null && plugin instanceof StonePluginInterface) {
            ((StonePluginInterface) plugin).onLicenseActivated();
        }
    }
    
    /**
     * Iniciar verificações periódicas
     */
    private void startPeriodicChecks() {
        // Verificar licenças a cada 30 minutos
        new BukkitRunnable() {
            @Override
            public void run() {
                if (apiConnected) {
                    validateAllLicenses();
                    checkForUpdates();
                }
            }
        }.runTaskTimerAsynchronously(this, 20L * 60L * 30L, 20L * 60L * 30L); // 30 minutos
        
        // Verificar conexão com API a cada 5 minutos
        new BukkitRunnable() {
            @Override
            public void run() {
                connectToAPI();
            }
        }.runTaskTimerAsynchronously(this, 20L * 60L * 5L, 20L * 60L * 5L); // 5 minutos
    }
    
    /**
     * Validar todas as licenças ativas
     */
    private void validateAllLicenses() {
        for (LicenseData license : activeLicenses.values()) {
            try {
                boolean isValid = validateLicenseWithAPI(license.getLicenseKey());
                if (!isValid) {
                    getLogger().warning("§c[StonePlugins Core] Licença inválida detectada: " + 
                                      license.getProductName());
                    deactivateLicense(license.getProductName());
                }
            } catch (Exception e) {
                getLogger().warning("§c[StonePlugins Core] Erro ao validar licença " + 
                                  license.getProductName() + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Validar licença com a API
     */
    private boolean validateLicenseWithAPI(String licenseKey) throws Exception {
        URL url = new URL(API_BASE_URL + LICENSE_ENDPOINT);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        String jsonData = String.format(
            "{\"license_key\":\"%s\",\"server_uuid\":\"%s\"}", 
            licenseKey, serverUUID
        );
        
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        int responseCode = connection.getResponseCode();
        return responseCode == 200;
    }
    
    /**
     * Desativar licença
     */
    private void deactivateLicense(String productName) {
        activeLicenses.remove(productName);
        
        // Desativar plugins dependentes
        for (PluginInfo pluginInfo : registeredPlugins.values()) {
            if (pluginInfo.getRequiredLicense().equals(productName)) {
                pluginInfo.setActive(false);
                
                // Desabilitar plugin
                org.bukkit.plugin.Plugin plugin = getServer().getPluginManager().getPlugin(pluginInfo.getPluginName());
                if (plugin != null) {
                    getServer().getPluginManager().disablePlugin(plugin);
                    getLogger().warning("§c[StonePlugins Core] Plugin " + pluginInfo.getPluginName() + 
                                      " desabilitado devido à licença inválida!");
                }
            }
        }
    }
    
    /**
     * Verificar atualizações
     */
    private void checkForUpdates() {
        // Implementar verificação de atualizações
        // Por enquanto, apenas log
        getLogger().info("§e[StonePlugins Core] Verificando atualizações...");
    }
    
    /**
     * Notificar API sobre desligamento do servidor
     */
    private void notifyServerShutdown() {
        if (!apiConnected) return;
        
        try {
            URL url = new URL(API_BASE_URL + "/server/shutdown");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            
            String jsonData = String.format("{\"server_uuid\":\"%s\"}", serverUUID);
            
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            connection.getResponseCode(); // Apenas para executar a requisição
            
        } catch (Exception e) {
            // Ignorar erros no shutdown
        }
    }
    
    // ==================== API PÚBLICA ====================
    
    /**
     * Verificar se um produto tem licença ativa
     * Esta função será usada pelos outros plugins
     */
    public static boolean hasActiveLicense(String productName) {
        StonePluginsCore instance = (StonePluginsCore) Bukkit.getPluginManager().getPlugin("StonePlugins-Core");
        if (instance == null) {
            return false;
        }
        
        LicenseData license = instance.activeLicenses.get(productName);
        return license != null && license.isActive() && !license.isExpired();
    }
    
    /**
     * Registrar plugin para verificação de licença
     */
    public static void registerPlugin(String pluginName, String version, String requiredLicense) {
        StonePluginsCore instance = (StonePluginsCore) Bukkit.getPluginManager().getPlugin("StonePlugins-Core");
        if (instance != null) {
            PluginInfo pluginInfo = new PluginInfo(pluginName, version, requiredLicense);
            boolean hasLicense = instance.activeLicenses.containsKey(requiredLicense);
            pluginInfo.setActive(hasLicense);
            
            instance.registeredPlugins.put(pluginName, pluginInfo);
            
            if (hasLicense) {
                instance.getLogger().info("§a[StonePlugins Core] Plugin " + pluginName + " registrado e ativado!");
            } else {
                instance.getLogger().warning("§c[StonePlugins Core] Plugin " + pluginName + 
                                           " registrado mas sem licença válida!");
            }
        }
    }
    
    /**
     * Obter instância do plugin
     */
    public static StonePluginsCore getInstance() {
        return (StonePluginsCore) Bukkit.getPluginManager().getPlugin("StonePlugins-Core");
    }
}