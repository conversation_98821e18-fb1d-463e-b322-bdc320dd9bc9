const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Utils = require('../utils');
const { SupabaseLicenseManager } = require('../supabase');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('aprovar')
        .setDescription('Aprovar manualmente o pagamento deste canal')
        .setDefaultMemberPermissions('0'),

    async execute(interaction) {
        // Logs apenas em desenvolvimento
        if (process.env.NODE_ENV !== 'production') {
            console.log(`🔧 Comando aprovar: ${interaction.user.tag}`);
        }
        
        // Verificar se é admin (pular verificação se ADMIN_ROLE_ID não estiver configurado)
        if (process.env.ADMIN_ROLE_ID && !interaction.member.roles.cache.has(process.env.ADMIN_ROLE_ID)) {
            return interaction.reply({
                embeds: [Utils.createErrorEmbed('Você não tem permissão para usar este comando!')],
                flags: 64 // Ephemeral flag
            });
        }

        try {
            const database = interaction.client.database;
            const supabaseLicenseManager = new SupabaseLicenseManager();

            // Buscar venda pelo canal atual
            let sale = null;

            if (database.isDatabaseAvailable()) {
                sale = await database.getSaleByChannelId(interaction.channel.id);
            } else {
                // Buscar no Supabase quando SQLite não estiver disponível
                try {
                    sale = await supabaseLicenseManager.getSaleByChannelId(interaction.channel.id);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            if (!sale) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Nenhuma venda encontrada neste canal!')],
                    flags: 64 // Ephemeral flag
                });
            }

            if (sale.status === 'completed') {
                return interaction.reply({
                    embeds: [Utils.createWarningEmbed('Esta venda já foi aprovada!')],
                    flags: 64 // Ephemeral flag
                });
            }

            if (sale.status === 'cancelled') {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Esta venda foi cancelada!')],
                    flags: 64 // Ephemeral flag
                });
            }

            // Buscar produto (SQLite local ou Supabase)
            let product = null;

            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(sale.product_id);
            } else {
                // Buscar produto no Supabase quando SQLite não estiver disponível
                try {
                    const result = await supabaseLicenseManager.getProductById(sale.product_id);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price)
                        };
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                return interaction.reply({
                    embeds: [Utils.createErrorEmbed('Produto não encontrado!')],
                    flags: 64 // Ephemeral flag
                });
            }

            // Mostrar informações da venda para confirmação
            const embed = new EmbedBuilder()
                .setTitle('<:warning:1399460575117053952> Confirmar Aprovação Manual')
                .setDescription('Você está prestes a aprovar manualmente esta venda:')
                .addFields(
                    { name: '<:receipt:1399850763487805628> ID da Venda', value: `\`${sale.id}\``, inline: true },
                    { name: '<:user:1399896025304666133> Cliente', value: `<@${sale.user_id}> (${sale.username})`, inline: true },
                    { name: '<:cardbox:1399850629219880990> Produto', value: product.title, inline: true },
                    { name: '<:coin:1399460496213540884> Valor', value: Utils.formatPrice(sale.amount), inline: true },
                    { name: '<:calendardays:1399853724016316447> Data da Compra', value: Utils.formatDate(new Date(sale.created_at)), inline: true },
                    { name: '<:cloudcog:1399852646894145691> Status Atual', value: sale.status, inline: true }
                )
                .setColor(Utils.getEmbedColor('Warning'))
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`confirm_approve_${sale.id}`)
                        .setLabel('Confirmar Aprovação')
                        .setEmoji('<:check:1399460320854151230>')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('cancel_approve')
                        .setLabel('Cancelar')
                        .setEmoji('<:cancel:1399851210575712448>')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row],
                flags: 64 // Ephemeral flag
            });

        } catch (error) {
            console.error('Erro ao processar comando aprovar:', error);
            await interaction.reply({
                embeds: [Utils.createErrorEmbed('Erro interno. Tente novamente.')],
                flags: 64 // Ephemeral flag
            });
        }
    },

    async handleButton(interaction, database) {
        const customId = interaction.customId;

        if (customId === 'cancel_approve') {
            const embed = new EmbedBuilder()
                .setTitle('<:cancel:1399851210575712448> Aprovação Cancelada')
                .setDescription('A aprovação manual foi cancelada.')
                .setColor(Utils.getEmbedColor('Secondary'))
                .setTimestamp();

            await interaction.update({
                embeds: [embed],
                components: []
            });
            return;
        }

        if (customId.startsWith('confirm_approve_')) {
            const saleId = customId.replace('confirm_approve_', '');
            await this.approvePayment(interaction, database, saleId);
        }
    },

    async approvePayment(interaction, database, saleId) {
        try {
            const supabaseLicenseManager = new SupabaseLicenseManager();

            // Buscar venda (SQLite local ou Supabase)
            let sale = null;

            if (database.isDatabaseAvailable()) {
                sale = await database.getSale(saleId);
            } else {
                try {
                    sale = await supabaseLicenseManager.getSale(saleId);
                } catch (error) {
                    console.error('Erro ao buscar venda no Supabase:', error);
                }
            }

            if (!sale) {
                console.error('Venda não encontrada:', saleId);
                return;
            }

            // Buscar produto (SQLite local ou Supabase)
            let product = null;

            if (database.isDatabaseAvailable()) {
                product = await database.getProduct(sale.product_id);
            } else {
                try {
                    const result = await supabaseLicenseManager.getProductById(sale.product_id);
                    if (result) {
                        product = {
                            id: result.id,
                            title: result.name,
                            description: result.description,
                            price: parseFloat(result.price),
                            download_link: result.download_link // ADICIONADO: Link de download do Supabase
                        };
                        console.log(`✅ Produto encontrado com download_link: ${result.download_link}`);
                    }
                } catch (error) {
                    console.error('Erro ao buscar produto no Supabase:', error);
                }
            }

            if (!product) {
                console.error('Produto não encontrado:', sale.product_id);
                return;
            }

            // Atualizar status da venda (SQLite local ou Supabase)
            if (database.isDatabaseAvailable()) {
                await database.updateSaleStatus(saleId, 'completed', 'manual_approval');
            } else {
                try {
                    await supabaseLicenseManager.updateSaleStatus(saleId, 'completed', 'manual_approval');
                } catch (error) {
                    console.error('Erro ao atualizar status da venda no Supabase:', error);
                }
            }

            // Gerar licença
            const licenseKey = Utils.generateLicenseKey();

            // Criar licença no Supabase

            try {
                // Criar ou buscar usuário no Supabase
                const supabaseUser = await supabaseLicenseManager.createOrGetUser(
                    sale.user_id,
                    sale.username,
                    null // email não disponível
                );

                // Criar ou buscar produto no Supabase
                const supabaseProduct = await supabaseLicenseManager.createOrGetProduct(
                    product.title,
                    '1.0.0', // versão padrão
                    product.description,
                    parseFloat(product.price)
                );

                // Criar licença no Supabase
                const supabaseLicense = await supabaseLicenseManager.createLicense(
                    licenseKey,
                    supabaseUser.id,
                    supabaseProduct.id,
                    null // licença vitalícia
                );

                console.log('✅ [APROVAR] Licença criada no Supabase com sucesso');
                console.log(`   🔑 License Key: ${licenseKey}`);
                console.log(`   👤 Usuário: ${supabaseUser.username} (${supabaseUser.discord_id})`);
                console.log(`   📦 Produto: ${supabaseProduct.name} v${supabaseProduct.version}`);

            } catch (error) {
                console.error('❌ [APROVAR] Erro ao criar licença no Supabase:', error);
                throw new Error(`Falha ao criar licença no Supabase: ${error.message}`);
            }

            // Atualizar estoque se não for infinito
            if (product.stock > 0) {
                await database.updateProductStock(product.id, product.stock - 1);
            }

            // RESPOSTA IMEDIATA para evitar timeout
            const processingEmbed = new EmbedBuilder()
                .setTitle('<:loading:1399460437572976820> Processando Aprovação...')
                .setDescription('Criando licença e enviando para o cliente...')
                .setColor(Utils.getEmbedColor('Warning'))
                .setTimestamp();

            await interaction.update({
                embeds: [processingEmbed],
                components: []
            });

            // Processar o resto em background
            setTimeout(async () => {
                try {
                    // Confirmar aprovação para o admin
                    const adminEmbed = new EmbedBuilder()
                        .setTitle('<:check:1399460320854151230> Pagamento Aprovado Manualmente')
                        .setDescription('A venda foi aprovada com sucesso!')
                        .addFields(
                            { name: '<:receipt:1399850763487805628> ID da Venda', value: `\`${sale.id}\``, inline: true },
                            { name: '<:user:1399896025304666133> Cliente', value: `<@${sale.user_id}>`, inline: true },
                            { name: '<:cardbox:1399850629219880990> Produto', value: product.title, inline: true },
                            { name: '<:sparks:1399854274254733404> Licença Gerada', value: `\`${licenseKey}\``, inline: false },
                            { name: '<:calendardays:1399853724016316447> Válida até', value: 'LIFETIME (Vitalícia)', inline: true }
                        )
                        .setColor(Utils.getEmbedColor('Success'))
                        .setTimestamp();

                    await interaction.editReply({
                        embeds: [adminEmbed],
                        components: []
                    });
                } catch (editError) {
                    console.log('⚠️ Não foi possível atualizar mensagem do admin (normal se demorou muito)');
                }
            }, 1000);

            // Encontrar o canal da venda e enviar confirmação
            try {
                const channel = await interaction.guild.channels.fetch(sale.channel_id);
                if (channel) {
                    await this.sendApprovalToChannel(channel, sale, product, licenseKey);
                }
            } catch (error) {
                console.log('Canal da venda não encontrado ou já foi deletado');
            }

            // Enviar por DM para o cliente
            try {
                const user = await interaction.client.users.fetch(sale.user_id);
                await this.sendApprovalDM(user, sale, product, licenseKey);
            } catch (error) {
                console.log('Não foi possível enviar DM para o cliente');
            }

        } catch (error) {
            console.error('Erro ao aprovar pagamento:', error);
            await interaction.update({
                embeds: [Utils.createErrorEmbed('Erro ao aprovar pagamento. Tente novamente.')],
                components: []
            });
        }
    },

    async sendApprovalToChannel(channel, sale, product, licenseKey) {
        const successEmbed = new EmbedBuilder()
            .setTitle('<:check:1399460320854151230> Pagamento Aprovado Manualmente!')
            .setDescription('Sua compra foi aprovada por um administrador!\n\n📧 **Verifique sua DM para receber os dados da licença.**\n\n⏰ **Este canal será fechado automaticamente em 3 segundos.**')
            .addFields(
                { name: '<:cardbox:1399850629219880990> Produto', value: product.title, inline: true },
                { name: '<:coin:1399460496213540884> Valor', value: Utils.formatPrice(sale.amount), inline: true },
                { name: '<:calendardays:1399853724016316447> Data de Aprovação', value: Utils.formatDate(new Date()), inline: true },
                { name: '<:sparks:1399854274254733404> Sua Licença', value: `\`${licenseKey}\``, inline: false },
                { name: '<:calendardays:1399853724016316447> Válida até', value: 'LIFETIME (Vitalícia)', inline: true }
            )
            .setColor(Utils.getEmbedColor('Success'))
            .setTimestamp();

        const successMessage = await channel.send({
            content: `<@${sale.user_id}>`,
            embeds: [successEmbed]
        });

        // Iniciar contagem regressiva para fechar o canal automaticamente
        this.startChannelCloseCountdown(channel, successMessage, 3);
    },

    // Método para fechamento automático do canal
    async startChannelCloseCountdown(channel, message, seconds) {
        console.log(`🔄 [AUTO-CLOSE-MANUAL] Iniciando contagem regressiva de ${seconds} segundos para canal ${channel.name} (ID: ${channel.id})`);

        let remainingSeconds = seconds;

        const updateCountdown = async () => {
            try {
                // Verificar se o canal ainda existe
                if (!channel || channel.deleted) {
                    console.log('⚠️ [AUTO-CLOSE-MANUAL] Canal já foi deletado, cancelando contagem regressiva');
                    return;
                }

                if (remainingSeconds <= 0) {
                    console.log('🔒 [AUTO-CLOSE-MANUAL] Tempo esgotado, fechando canal...');

                    // Mostrar mensagem final antes de fechar
                    const finalEmbed = new EmbedBuilder()
                        .setTitle('<:othertrash:1399461117243166914> Fechando Canal')
                        .setDescription('Canal sendo fechado automaticamente...')
                        .setColor(Utils.getEmbedColor('Secondary'))
                        .setTimestamp();

                    try {
                        await message.edit({
                            embeds: [finalEmbed]
                        });
                        console.log('✅ [AUTO-CLOSE-MANUAL] Mensagem final enviada');
                    } catch (editError) {
                        console.log('⚠️ [AUTO-CLOSE-MANUAL] Erro ao editar mensagem final, prosseguindo com fechamento:', editError.message);
                    }

                    // Aguardar 2 segundos e fechar o canal
                    setTimeout(async () => {
                        try {
                            if (channel && !channel.deleted) {
                                await channel.delete('Aprovação manual concluída - fechamento automático');
                                console.log(`✅ [AUTO-CLOSE-MANUAL] Canal ${channel.name} fechado automaticamente após aprovação manual`);
                            } else {
                                console.log('⚠️ [AUTO-CLOSE-MANUAL] Canal já estava deletado');
                            }
                        } catch (error) {
                            console.error('❌ [AUTO-CLOSE-MANUAL] Erro ao deletar canal automaticamente:', error);
                        }
                    }, 3000);

                    return;
                }

                // Atualizar embed com contagem regressiva
                const countdownEmbed = new EmbedBuilder()
                    .setTitle('<:check:1399460320854151230> Pagamento Aprovado Manualmente!')
                    .setDescription(`Sua compra foi aprovada por um administrador!\n\n📧 **Verifique sua DM para receber os dados da licença.**\n\n⏰ **Este canal será fechado automaticamente em ${remainingSeconds} segundos.**`)
                    .setColor(Utils.getEmbedColor('Success'))
                    .setTimestamp();

                try {
                    await message.edit({
                        embeds: [countdownEmbed]
                    });
                    console.log(`⏰ [AUTO-CLOSE-MANUAL] Contagem regressiva: ${remainingSeconds} segundos restantes`);
                } catch (editError) {
                    console.error('❌ [AUTO-CLOSE-MANUAL] Erro ao atualizar contagem regressiva:', editError.message);
                    // Se não conseguir editar, continuar a contagem mesmo assim
                }

                remainingSeconds--;

                // Agendar próxima atualização
                setTimeout(updateCountdown, 1000);

            } catch (error) {
                console.error('❌ [AUTO-CLOSE-MANUAL] Erro geral na contagem regressiva:', error);
                // Se houver erro, tentar fechar o canal mesmo assim após 5 segundos
                setTimeout(async () => {
                    try {
                        if (channel && !channel.deleted) {
                            await channel.delete('Erro na contagem regressiva - fechamento forçado');
                            console.log(`✅ [AUTO-CLOSE-MANUAL] Canal ${channel.name} fechado após erro na contagem regressiva`);
                        }
                    } catch (deleteError) {
                        console.error('❌ [AUTO-CLOSE-MANUAL] Erro ao deletar canal após erro na contagem:', deleteError);
                    }
                }, 5000);
            }
        };

        // Iniciar contagem regressiva após 1 segundo
        console.log('🚀 [AUTO-CLOSE-MANUAL] Iniciando contagem regressiva...');
        setTimeout(updateCountdown, 1000);
    },

    async sendApprovalDM(user, sale, product, licenseKey) {
        const successEmbed = new EmbedBuilder()
            .setTitle('<:check:1399460320854151230> Pagamento Aprovado!')
            .setDescription('Sua compra foi aprovada manualmente por um administrador!')
            .addFields(
                { name: '<:cardbox:1399850629219880990> Produto', value: product.title, inline: true },
                { name: '<:coin:1399460496213540884> Valor', value: Utils.formatPrice(sale.amount), inline: true },
                { name: '<:calendardays:1399853724016316447> Data de Aprovação', value: Utils.formatDate(new Date()), inline: true },
                { name: '<:sparks:1399854274254733404> Sua Licença', value: `\`${licenseKey}\``, inline: false },
                { name: '<:calendardays:1399853724016316447> Válida até', value: '**LIFETIME (Vitalícia)**', inline: true }
            )
            .setColor(Utils.getEmbedColor('Success'))
            .setTimestamp();

        const instructionsEmbed = new EmbedBuilder()
            .setTitle('<:calendardays:1399853724016316447> Como usar sua licença')
            .setDescription(`
**Passos para ativar:**
1. Baixe o plugin!!
2. Coloque na pasta \`plugins\` do seu servidor
3. Reinicie o servidor
4. Use o comando: \`/sp license ${licenseKey}\`
5. Pronto! O plugin estará ativo

**Suporte:**
• Discord: [Servidor de Suporte](https://discord.gg/stoneplugins)
• Documentação: https://docs.stoneplugins.com
            `)
            .setColor(Utils.getEmbedColor('Info'));

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Download')
                    .setEmoji('<:download:1399855327352066159>')
                    .setStyle(ButtonStyle.Link)
                    .setURL((() => {
                        const downloadUrl = product.download_link || `https://github.com/stoneplugins/downloads/releases/tag/${product.id}`;
                        console.log(`🔗 Link de download usado: ${downloadUrl}`);
                        return downloadUrl;
                    })()),
                new ButtonBuilder()
                    .setLabel('Documentação')
                    .setStyle(ButtonStyle.Link)
                    .setEmoji('<:82321ttsexternallink:1399855446180888706>')
                    .setURL('https://docs.stoneplugins.com')
            );

        await user.send({
            embeds: [successEmbed, instructionsEmbed],
            components: [row]
        });
    }
};