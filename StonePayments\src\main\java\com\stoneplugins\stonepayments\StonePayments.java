package com.stoneplugins.stonepayments;

import com.stoneplugins.core.StonePluginInterface;
import com.stoneplugins.core.StonePluginsCore;
import com.stoneplugins.stonepayments.commands.PaymentCommand;
import com.stoneplugins.stonepayments.commands.ShopCommand;
import com.stoneplugins.stonepayments.config.ConfigManager;
import com.stoneplugins.stonepayments.config.ConfigMigrator;
import com.stoneplugins.stonepayments.database.DatabaseManager;
import com.stoneplugins.stonepayments.integrations.DiscordWebhook;
import com.stoneplugins.stonepayments.listeners.MenuListener;
import com.stoneplugins.stonepayments.managers.AnnouncementManager;
import com.stoneplugins.stonepayments.managers.PaymentManager;
import com.stoneplugins.stonepayments.managers.ProductManager;
import com.stoneplugins.stonepayments.managers.QRCodeManager;
import com.stoneplugins.stonepayments.mercadopago.MercadoPagoAPI;
import com.stoneplugins.stonepayments.updater.UpdateChecker;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

public class StonePayments extends JavaPlugin implements StonePluginInterface {

    private static StonePayments instance;

    private ConfigManager configManager;
    private ConfigMigrator configMigrator;
    private DatabaseManager databaseManager;
    private MercadoPagoAPI mercadoPagoAPI;
    private PaymentManager paymentManager;
    private ProductManager productManager;
    private QRCodeManager qrCodeManager;
    private AnnouncementManager announcementManager;
    private DiscordWebhook discordWebhook;
    private UpdateChecker updateChecker;
    
    // Sistema de licenças
    private boolean licenseActive = false;
    private boolean corePluginAvailable = false;

    @Override
    public void onEnable() {
        instance = this;

        getLogger().info("§6========================================");
        getLogger().info("§6    StonePayments v" + getDescription().getVersion());
        getLogger().info("§6    Sistema de Pagamento via PIX");
        getLogger().info("§6    Desenvolvido por StonePlugins");
        getLogger().info("§6========================================");

        try {
            // Verificar se StonePlugins-Core está disponível
            if (!checkCorePlugin()) {
                getLogger().severe("§c[StonePayments] StonePlugins-Core não encontrado!");
                getLogger().severe("§c[StonePayments] Este plugin requer StonePlugins-Core para funcionar.");
                getLogger().severe("§c[StonePayments] Download: https://stoneplugins.com/core");
                getServer().getPluginManager().disablePlugin(this);
                return;
            }
            
            // Registrar plugin no sistema de licenças
            StonePluginsCore.registerPlugin(getName(), getDescription().getVersion(), getRequiredLicenseName());
            
            // Verificar licença
            if (!checkLicense()) {
                getLogger().warning("§c[StonePayments] Licença não encontrada!");
                getLogger().warning("§c[StonePayments] Use /stoneplugins ativar <licença> para ativar.");
                getLogger().warning("§c[StonePayments] Plugin funcionará em modo limitado.");
            }

            // Migrar configurações (preserva configurações existentes)
            configMigrator = new ConfigMigrator(this);
            configMigrator.migrateConfigurations();

            // Inicializar configurações
            configManager = new ConfigManager(this);
            configManager.loadConfigs();

            // Inicializar sistema baseado na licença
            if (licenseActive) {
                initializeFullSystem();
            } else {
                initializeLimitedSystem();
            }

            // Registrar comandos
            getCommand("comprar").setExecutor(new ShopCommand(this));
            getCommand("stonepayments").setExecutor(new PaymentCommand(this));

            // Registrar listeners
            Bukkit.getPluginManager().registerEvents(new MenuListener(this), this);

            getLogger().info("§aStonePayments habilitado!");
            getLogger().info("§aVersão: " + getDescription().getVersion());
            if (licenseActive) {
                getLogger().info("§a Licença ativa - Todas as funcionalidades disponíveis!");
            } else {
                getLogger().warning("§e Modo limitado - Algumas funcionalidades restritas!");
                getLogger().warning("§e[StonePayments] Para ativar: /stoneplugins ativar <licença>");
            }

        } catch (Exception e) {
            getLogger().severe("§c[StonePayments] Erro ao inicializar plugin: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }

    @Override
    public void onDisable() {
        if (paymentManager != null) {
            paymentManager.stopPaymentChecker();
        }

        if (databaseManager != null) {
            databaseManager.close();
        }

        getLogger().info("§6StonePayments desabilitado!");
    }
    
    /**
     * Verificar se StonePlugins-Core está disponível
     */
    private boolean checkCorePlugin() {
        org.bukkit.plugin.Plugin corePlugin = getServer().getPluginManager().getPlugin("StonePlugins-Core");
        corePluginAvailable = corePlugin != null && corePlugin.isEnabled();
        return corePluginAvailable;
    }
    
    /**
     * Verificar licença
     */
    private boolean checkLicense() {
        if (!corePluginAvailable) {
            return false;
        }
        
        licenseActive = StonePluginsCore.hasActiveLicense(getRequiredLicenseName());
        return licenseActive;
    }
    
    /**
     * Inicializar sistema completo (com licença)
     */
    private void initializeFullSystem() {
        getLogger().info("§a[StonePayments] Inicializando sistema completo...");
        
        try {
            // Inicializar banco de dados
            databaseManager = new DatabaseManager(this);
            databaseManager.initialize();

            // Inicializar API do Mercado Pago
            mercadoPagoAPI = new MercadoPagoAPI(this);

            // Inicializar gerenciadores
            qrCodeManager = new QRCodeManager(this);
            paymentManager = new PaymentManager(this);
            productManager = new ProductManager(this);
            announcementManager = new AnnouncementManager(this);
            discordWebhook = new DiscordWebhook(this);

            // Inicializar sistema de atualizações
            updateChecker = new UpdateChecker(this);

            // Iniciar verificação de pagamentos
            paymentManager.startPaymentChecker();

            // Verificar atualizações (se habilitado)
            if (configManager.getConfig().getBoolean("updater.check_updates", true)) {
                Bukkit.getScheduler().runTaskLaterAsynchronously(this, this::checkForUpdates, 100L);
            }

            // Verificar configurações importantes
            if (!mercadoPagoAPI.isTokenValid()) {
                getLogger().warning("§e Configure o token do Mercado Pago para usar o plugin!");
                getLogger().warning("§eEdite o arquivo config.yml na pasta plugins/StonePayments/");
            } else {
                getLogger().info("§a API do Mercado Pago conectada com sucesso!");
            }

            // Verificar configuração do Discord
            if (configManager.getConfig().getBoolean("discord.enabled", false)) {
                String publicWebhook = configManager.getConfig().getString("discord.public_webhook");
                String adminWebhook = configManager.getConfig().getString("discord.admin_webhook");

                if ((publicWebhook != null && !publicWebhook.equals("SEU_WEBHOOK_PUBLICO_AQUI")) ||
                    (adminWebhook != null && !adminWebhook.equals("SEU_WEBHOOK_ADMIN_AQUI"))) {
                    getLogger().info("§a Discord Webhook configurado!");
                } else {
                    getLogger().warning("§e Discord habilitado mas webhooks não configurados!");
                }
            }
            
            getLogger().info("§a[StonePayments] Sistema completo inicializado!");
            
        } catch (Exception e) {
            getLogger().severe("§c[StonePayments] Erro ao inicializar sistema completo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Inicializar sistema limitado (sem licença)
     */
    private void initializeLimitedSystem() {
        getLogger().warning("§e[StonePayments] Inicializando sistema limitado...");
        
        try {
            // No modo limitado, apenas configurações básicas
            databaseManager = new DatabaseManager(this);
            databaseManager.initialize();
            
            // Mostrar mensagem de limitação
            showLimitationMessage();
            
        } catch (Exception e) {
            getLogger().severe("§c[StonePayments] Erro ao inicializar sistema limitado: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Mostrar mensagem de limitação
     */
    private void showLimitationMessage() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("stonepayments.admin")) {
                        player.sendMessage("§c§l[StonePayments] §cPlugin em modo limitado!");
                        player.sendMessage("§e[StonePayments] Ative a licença para processar pagamentos.");
                        player.sendMessage("§e[StonePayments] Use: §f/stoneplugins ativar <licença>");
                    }
                }
            }
        }.runTaskLater(this, 20L * 5L); // 5 segundos após iniciar
    }

    private void checkForUpdates() {
        if (!licenseActive) {
            return; // Não verificar atualizações sem licença
        }
        
        updateChecker.checkForUpdates().thenAccept(updateInfo -> {
            if (updateInfo.hasUpdate()) {
                updateChecker.notifyUpdate(updateInfo);

                // Agendar próxima verificação
                int checkInterval = configManager.getConfig().getInt("updater.check_interval", 24);
                Bukkit.getScheduler().runTaskLaterAsynchronously(this,
                    this::checkForUpdates, 20L * 60L * 60L * checkInterval);
            }
        });
    }
    
    /**
     * Verificar se funcionalidade requer licença
     */
    public boolean requiresLicense(String feature) {
        if (licenseActive) {
            return false; // Licença ativa, todas as funcionalidades liberadas
        }
        
        // Funcionalidades que requerem licença
        return feature.equals("payment") || 
               feature.equals("webhook") || 
               feature.equals("qrcode") ||
               feature.equals("advanced_commands");
    }

    // ==================== IMPLEMENTAÇÃO DA INTERFACE ====================
    
    @Override
    public void onLicenseActivated() {
        getLogger().info("§a[StonePayments] Licença ativada! Habilitando funcionalidades completas...");
        
        licenseActive = true;
        
        // Recarregar sistema completo
        new BukkitRunnable() {
            @Override
            public void run() {
                initializeFullSystem();
                
                // Notificar admins online
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("stonepayments.admin")) {
                        player.sendMessage("§a§l[StonePayments] §aLicença ativada com sucesso!");
                        player.sendMessage("§a[StonePayments] Sistema de pagamentos habilitado!");
                    }
                }
            }
        }.runTask(this);
    }
    
    @Override
    public void onLicenseDeactivated() {
        getLogger().warning("§c[StonePayments] Licença desativada! Desabilitando pagamentos...");
        
        licenseActive = false;
        
        // Desabilitar sistema de pagamentos
        new BukkitRunnable() {
            @Override
            public void run() {
                // Parar verificação de pagamentos
                if (paymentManager != null) {
                    paymentManager.stopPaymentChecker();
                }
                
                // Notificar admins online
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.hasPermission("stonepayments.admin")) {
                        player.sendMessage("§c§l[StonePayments] §cLicença desativada!");
                        player.sendMessage("§e[StonePayments] Sistema de pagamentos desabilitado.");
                    }
                }
                
                initializeLimitedSystem();
            }
        }.runTask(this);
    }
    
    @Override
    public String getRequiredLicenseName() {
        return "StonePayments"; // Nome do produto na licença
    }
    
    @Override
    public boolean canRunWithoutLicense() {
        return true; // Permite modo limitado
    }

    // ==================== GETTERS ====================

    public static StonePayments getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public ConfigMigrator getConfigMigrator() {
        return configMigrator;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public MercadoPagoAPI getMercadoPagoAPI() {
        return mercadoPagoAPI;
    }

    public PaymentManager getPaymentManager() {
        return paymentManager;
    }

    public ProductManager getProductManager() {
        return productManager;
    }

    public QRCodeManager getQRCodeManager() {
        return qrCodeManager;
    }

    public AnnouncementManager getAnnouncementManager() {
        return announcementManager;
    }

    public DiscordWebhook getDiscordWebhook() {
        return discordWebhook;
    }

    public UpdateChecker getUpdateChecker() {
        return updateChecker;
    }
    
    public boolean isLicenseActive() {
        return licenseActive;
    }
}