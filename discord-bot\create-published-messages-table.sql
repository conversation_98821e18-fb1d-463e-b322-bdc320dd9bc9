-- Criar tabela para rastrear mensagens publicadas de produtos
CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_channel_id ON published_messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_guild_id ON published_messages(guild_id);

-- Comentários para documentação
COMMENT ON TABLE published_messages IS 'Rastreia mensagens de produtos publicadas nos canais do Discord';
COMMENT ON COLUMN published_messages.message_id IS 'ID da mensagem no Discord';
COMMENT ON COLUMN published_messages.channel_id IS 'ID do canal onde a mensagem foi publicada';
COMMENT ON COLUMN published_messages.product_id IS 'ID do produto que foi publicado';
COMMENT ON COLUMN published_messages.guild_id IS 'ID do servidor Discord';