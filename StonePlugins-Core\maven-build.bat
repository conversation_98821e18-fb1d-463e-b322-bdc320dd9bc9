@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Compilando StonePlugins-Core com Maven
echo ========================================

REM Usar o Maven que existe no codebase
set "MAVEN_CMD=..\apache-maven-3.9.5\bin\mvn.cmd"

if not exist "%MAVEN_CMD%" (
    echo ERRO: Maven nao encontrado em %MAVEN_CMD%
    pause
    exit /b 1
)

echo Usando Maven: %MAVEN_CMD%
echo.

REM Compilar com Maven
echo Executando: mvn clean package
"%MAVEN_CMD%" clean package

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao Maven!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    COMPILACAO CONCLUIDA!
echo ========================================

REM Verificar se o JAR foi gerado
if not exist "target\StonePluginsDepend.jar" (
    echo ERRO: Arquivo JAR nao foi gerado!
    pause
    exit /b 1
)

echo Arquivo gerado: target\StonePluginsDepend.jar
for %%I in (target\StonePluginsDepend.jar) do echo Tamanho: %%~zI bytes
echo.

echo Copiando para o servidor...

REM Copiar para o servidor
set "SERVER_PLUGINS=..\servidor\plugins"
set "TARGET_FILE=%SERVER_PLUGINS%\StonePluginsDepend.jar"

REM Fazer backup se existir
if exist "%TARGET_FILE%" (
    set "BACKUP_FILE=%SERVER_PLUGINS%\stoneplugins-core-1.0.0-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.jar"
    set "BACKUP_FILE=!BACKUP_FILE: =0!"
    copy "%TARGET_FILE%" "!BACKUP_FILE!" >nul
    echo Backup criado: !BACKUP_FILE!
)

REM Remover versões antigas do StonePluginsDepend
echo Removendo versoes antigas...
del "%SERVER_PLUGINS%\StonePluginsDepend*.jar" 2>nul
del "%SERVER_PLUGINS%\stoneplugins-core-*.jar" 2>nul

REM Remover pasta de configuração antiga para forçar atualização
if exist "%SERVER_PLUGINS%\StonePlugins-Core" (
    echo Removendo pasta de configuracao antiga...
    rmdir /s /q "%SERVER_PLUGINS%\StonePlugins-Core" 2>nul
)

REM Copiar novo plugin
copy "target\StonePluginsDepend.jar" "%TARGET_FILE%" >nul

if exist "%TARGET_FILE%" (
    echo Plugin copiado para o servidor com sucesso!
    echo Localizacao: %TARGET_FILE%
    echo.
    echo ========================================
    echo    STONEPLUGINSDEPEND INSTALADO!
    echo ========================================
    echo.
    echo PROXIMOS PASSOS:
    echo 1. Reinicie o servidor para carregar o plugin
    echo 2. Use /stoneplugins status para verificar
    echo 3. Configure outros plugins como dependentes
    echo 4. Distribua licencas via Discord Bot
    echo.
    echo COMANDOS DISPONIVEIS:
    echo - /stoneplugins ativar ^<licenca^>
    echo - /stoneplugins status
    echo - /stoneplugins licencas
    echo - /stoneplugins plugins
    echo.
    echo IMPORTANTE:
    echo - Este plugin eh obrigatorio para outros StonePlugins
    echo - Configure a API no Discord Bot
    echo - Teste a ativacao de licencas
    echo.
) else (
    echo ERRO: Falha ao copiar plugin para o servidor.
    echo Plugin disponivel em: target\StonePluginsDepend.jar
)

pause