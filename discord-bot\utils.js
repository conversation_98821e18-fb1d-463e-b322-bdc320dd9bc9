const { v4: uuidv4 } = require('uuid');

class Utils {
    static generateId() {
        return uuidv4();
    }

    static generateLicenseKey() {
        const prefix = 'SP_';
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = prefix;
        
        for (let i = 0; i < 12; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return result;
    }

    static formatPrice(price) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(price);
    }

    static formatDate(date) {
        return new Intl.DateTimeFormat('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    static getButtonStyle(color) {
        const styles = {
            'Primary': 1,
            'Secondary': 2,
            'Success': 3,
            'Danger': 4
        };
        return styles[color] || 1;
    }

    static getEmbedColor(color) {
        const colors = {
            'Primary': 0x5865F2,
            'Secondary': 0x6C757D,
            'Success': 0x198754,
            'Danger': 0xDC3545,
            'Warning': 0xFFC107,
            'Info': 0x0DCAF0
        };
        return colors[color] || 0x5865F2;
    }

    static truncateText(text, maxLength = 100) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
    }

    static isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    static sanitizeInput(input) {
        return input.replace(/[<>@#&!]/g, '').trim();
    }

    static createProgressBar(current, total, length = 10) {
        const percentage = Math.min(current / total, 1);
        const filled = Math.round(length * percentage);
        const empty = length - filled;
        
        return '█'.repeat(filled) + '░'.repeat(empty);
    }

    static getTimeRemaining(expirationDate) {
        const now = new Date();
        const expiration = new Date(expirationDate);
        const diff = expiration - now;

        if (diff <= 0) return 'Expirado';

        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days} dia(s)`;
        if (hours > 0) return `${hours} hora(s)`;
        return `${minutes} minuto(s)`;
    }

    static async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    static chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    static generateChannelName(username, productTitle) {
        const cleanUsername = username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        const cleanProduct = productTitle.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        const timestamp = Date.now().toString().slice(-4);
        
        return `compra-${cleanProduct}-${cleanUsername}-${timestamp}`;
    }

    static createDownloadButton(productId, downloadLink = null) {
        // Se não tiver link de download, usar um link genérico mais apropriado
        const fallbackUrl = downloadLink || `https://github.com/stoneplugins/downloads/releases/tag/${productId}`;
        
        return {
            type: 2,
            style: 5, // Link style
            label: '📥 Download',
            url: fallbackUrl
        };
    }

    static createSupportButton() {
        return {
            type: 2,
            style: 2, // Secondary style
            label: '🎧 Suporte',
            custom_id: 'open_support_ticket'
        };
    }

    static validateProductData(data) {
        const errors = [];

        if (!data.title || data.title.length < 3) {
            errors.push('Título deve ter pelo menos 3 caracteres');
        }

        if (!data.description || data.description.length < 10) {
            errors.push('Descrição deve ter pelo menos 10 caracteres');
        }

        if (!data.price || data.price <= 0) {
            errors.push('Preço deve ser maior que zero');
        }

        if (data.banner_url && !this.isValidUrl(data.banner_url)) {
            errors.push('URL do banner inválida');
        }

        if (data.stock !== -1 && data.stock < 0) {
            errors.push('Estoque não pode ser negativo');
        }

        return errors;
    }

    static createErrorEmbed(message, title = 'Erro') {
        return {
            title: `❌ ${title}`,
            description: message,
            color: this.getEmbedColor('Danger'),
            timestamp: new Date().toISOString()
        };
    }

    static createSuccessEmbed(message, title = 'Sucesso') {
        return {
            title: `✅ ${title}`,
            description: message,
            color: this.getEmbedColor('Success'),
            timestamp: new Date().toISOString()
        };
    }

    static createWarningEmbed(message, title = 'Atenção') {
        return {
            title: `⚠️ ${title}`,
            description: message,
            color: this.getEmbedColor('Warning'),
            timestamp: new Date().toISOString()
        };
    }

    static createInfoEmbed(message, title = 'Informação') {
        return {
            title: `ℹ️ ${title}`,
            description: message,
            color: this.getEmbedColor('Info'),
            timestamp: new Date().toISOString()
        };
    }
}

module.exports = Utils;