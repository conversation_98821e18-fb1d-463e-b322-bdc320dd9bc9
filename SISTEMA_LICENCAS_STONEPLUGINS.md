# 🔐 Sistema de Licenças StonePlugins

## 📋 Visão Geral

Sistema completo de licenciamento para plugins Minecraft que impede pirataria através de um plugin central obrigatório.

### 🎯 Conceito Principal

1. **StonePlugins-Core** - Plugin central obrigatório
2. **Plugins Dependentes** - Todos os outros plugins dependem do Core
3. **Autenticação Centralizada** - Licenças validadas pelo Core
4. **Anti-Pirataria** - Sem o Core, nenhum plugin funciona

## 🏗️ Arquitetura do Sistema

### **1. StonePlugins-Core (Plugin Central)**
```
📦 StonePlugins-Core
├── 🔑 Sistema de autenticação
├── 🌐 Comunicação com API
├── 💾 Gerenciamento de licenças
├── 🔄 Verificação periódica
├── 🛡️ Sistema anti-pirataria
└── 📊 Monitoramento de uso
```

**Funcionalidades:**
- Comando `/stoneplugins ativar <licença>`
- Validação automática de licenças
- Identificação única do servidor
- Verificação de atualizações
- Desabilitação de plugins sem licença

### **2. Plugins Dependentes**
```
📦 SpawnerShop (Exemplo)
├── 🔗 depend: [StonePlugins-Core]
├── 🎭 Modo Limitado (sem licença)
├── 🚀 Modo Completo (com licença)
├── 🔄 Ativação automática
└── 📱 Interface StonePluginInterface
```

**Características:**
- **Dependência obrigatória** do Core
- **Modo demo** limitado sem licença
- **Ativação automática** quando licença é validada
- **Verificação contínua** de licença

### **3. Sistema de API**
```
🌐 API Endpoints
├── POST /api/license/activate
├── POST /api/license/validate  
├── POST /api/license/update-check
├── POST /api/server/shutdown
└── GET  /api/health
```

## 🔧 Como Funciona

### **Fluxo de Instalação:**

1. **Cliente baixa StonePlugins-Core**
2. **Cliente baixa plugin desejado (ex: SpawnerShop)**
3. **Plugin não funciona** sem o Core
4. **Cliente instala Core** como dependência
5. **Plugin funciona em modo limitado**

### **Fluxo de Ativação:**

1. **Cliente compra licença** no Discord
2. **Cliente recebe chave** via DM
3. **Cliente usa** `/stoneplugins ativar <chave>`
4. **Core valida** com API
5. **Plugin é ativado** automaticamente

### **Fluxo de Verificação:**

1. **Core verifica licenças** a cada 30 minutos
2. **API valida** servidor e licença
3. **Se inválida**, plugin é desabilitado
4. **Logs são gerados** para monitoramento

## 🛡️ Sistema Anti-Pirataria

### **Proteções Implementadas:**

1. **Dependência Obrigatória**
   - Sem Core = Sem funcionamento
   - Impossível remover dependência

2. **Identificação Única do Servidor**
   - UUID baseado em IP + Porta + Dados únicos
   - Uma licença = Um servidor

3. **Validação Contínua**
   - Verificação a cada 30 minutos
   - Desabilitação automática se inválida

4. **Modo Limitado**
   - Funcionalidades restritas sem licença
   - Incentiva compra da versão completa

## 📱 Interface de Comandos

### **Comandos do Core:**
```bash
/stoneplugins ativar <licença>    # Ativar licença
/stoneplugins status              # Ver status do sistema
/stoneplugins licencas            # Ver licenças ativas
/stoneplugins plugins             # Ver plugins registrados
/stoneplugins reload              # Recarregar sistema (admin)
```

### **Comandos dos Plugins:**
```bash
/spawnershop info                 # Informações do plugin
/spawnershop license              # Status da licença
/spawnershop shop                 # Abrir loja (limitada sem licença)
```

## 🔄 Integração com Discord Bot

### **Processo de Venda:**

1. **Cliente compra** via Discord
2. **Licença é gerada** automaticamente
3. **Cliente recebe DM** com instruções
4. **Licença é salva** no Supabase
5. **API está pronta** para validação

### **Dados Salvos:**
```json
{
  "license_key": "sp_abc123...",
  "product_id": "spawnershop-uuid",
  "user_id": "discord-user-id",
  "server_uuid": null,
  "server_ip": null,
  "activated_at": null,
  "expires_at": null,
  "status": "pending"
}
```

## 🚀 Implementação

### **1. Instalar StonePlugins-Core**
```yaml
# plugin.yml
name: StonePlugins-Core
main: com.stoneplugins.core.StonePluginsCore
version: 1.0.0
commands:
  stoneplugins:
    description: Comandos do sistema de licenças
```

### **2. Criar Plugin Dependente**
```yaml
# plugin.yml
name: SpawnerShop
main: com.stoneplugins.spawnershop.SpawnerShopLicensed
depend: [StonePlugins-Core]  # OBRIGATÓRIO
extra:
  required-license: "SpawnerShop"
```

### **3. Implementar Interface**
```java
public class SpawnerShopLicensed extends JavaPlugin implements StonePluginInterface {
    
    @Override
    public void onLicenseActivated() {
        // Ativar funcionalidades completas
    }
    
    @Override
    public String getRequiredLicenseName() {
        return "SpawnerShop";
    }
}
```

### **4. Verificar Licença**
```java
// No onEnable do plugin
if (!StonePluginsCore.hasActiveLicense("SpawnerShop")) {
    // Modo limitado
    initializeLimitedMode();
} else {
    // Modo completo
    initializeFullMode();
}
```

## 📊 Benefícios do Sistema

### **Para o Desenvolvedor:**
- ✅ **Proteção contra pirataria**
- ✅ **Controle centralizado**
- ✅ **Monitoramento de uso**
- ✅ **Atualizações automáticas**
- ✅ **Estatísticas detalhadas**

### **Para o Cliente:**
- ✅ **Instalação simples**
- ✅ **Ativação fácil**
- ✅ **Modo demo disponível**
- ✅ **Suporte integrado**
- ✅ **Atualizações automáticas**

## 🔧 Configuração da API

### **Endpoints Necessários:**
```javascript
// Ativar licença
POST /api/license/activate
{
  "license_key": "sp_abc123...",
  "server_uuid": "server-uuid",
  "server_ip": "127.0.0.1",
  "server_port": 25565
}

// Validar licença
POST /api/license/validate
{
  "license_key": "sp_abc123...",
  "server_uuid": "server-uuid"
}
```

### **Respostas da API:**
```javascript
// Sucesso
{
  "success": true,
  "product_name": "SpawnerShop",
  "expires_at": "2025-12-31T23:59:59Z"
}

// Erro
{
  "success": false,
  "error": "Licença não encontrada"
}
```

## 📈 Estatísticas e Monitoramento

### **Dados Coletados:**
- Servidores ativos por produto
- Uso por região/país
- Versões mais utilizadas
- Tentativas de pirataria
- Performance dos plugins

### **Relatórios Disponíveis:**
- Dashboard de vendas
- Mapa de servidores ativos
- Alertas de segurança
- Estatísticas de uso

## 🎯 Resultado Final

### **Sistema Profissional:**
- ✅ Proteção robusta contra pirataria
- ✅ Experiência de usuário simplificada
- ✅ Controle total sobre licenças
- ✅ Escalabilidade para múltiplos produtos
- ✅ Integração completa com Discord

### **Fluxo do Cliente:**
1. **Compra** no Discord → Recebe licença
2. **Instala** plugins → Funciona em modo demo
3. **Ativa** licença → Funcionalidades completas
4. **Usa** normalmente → Verificação automática

**O sistema cria uma dependência obrigatória que torna a pirataria praticamente impossível, enquanto oferece uma experiência profissional para clientes legítimos!** 🚀