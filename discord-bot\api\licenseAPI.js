const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const path = require('path');
const Utils = require('../utils');

class LicenseAPI {
    constructor(database) {
        this.database = database;
        this.app = express();
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // Configurar trust proxy para Render
        if (process.env.NODE_ENV === 'production' || process.env.RENDER) {
            this.app.set('trust proxy', 1);
        }

        // CORS
        this.app.use(cors({
            origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
        }));

        // JSON parser
        this.app.use(express.json());

        // Servir arquivos estáticos
        this.app.use(express.static(path.join(__dirname, '..', 'public')));

        // Rate limiting apenas para API
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutos
            max: 100, // máximo 100 requests por IP
            message: {
                error: 'Too many requests',
                message: 'Rate limit exceeded. Try again later.'
            }
        });
        this.app.use('/api', limiter);

        // Request logging
        this.app.use('/api', (req, res, next) => {
            console.log(`[API] ${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`);
            next();
        });

        // API Key authentication middleware
        this.app.use('/api', async (req, res, next) => {
            const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
            
            if (!apiKey) {
                return res.status(401).json({
                    error: 'Unauthorized',
                    message: 'API key required'
                });
            }

            try {
                const keyData = await this.database.validateApiKey(apiKey);
                if (!keyData) {
                    return res.status(401).json({
                        error: 'Unauthorized',
                        message: 'Invalid API key'
                    });
                }

                req.apiKey = keyData;
                console.log(`[API] Authenticated with key: ${keyData.key_name}`);
                next();
            } catch (error) {
                console.error('Error validating API key:', error);
                return res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error validating API key'
                });
            }
        });
    }

    setupRoutes(app = null) {
        const targetApp = app || this.app;
        
        // Importar e configurar API de validação de licenças
        const LicenseValidationAPI = require('./licenseValidationAPI');
        const licenseValidationAPI = new LicenseValidationAPI(this.database);
        targetApp.use('/api/license', licenseValidationAPI.getRouter());

        // Rota principal - Status do sistema (apenas se não for integração)
        if (!app) {
            targetApp.get('/', (req, res) => {
            const uptime = process.uptime();
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = Math.floor(uptime % 60);

            res.json({
                status: 'online',
                service: 'StonePlugins License System',
                uptime: `${hours}h ${minutes}m ${seconds}s`,
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                endpoints: {
                    health: '/health',
                    ping: '/ping',
                    status: '/status',
                    docs: '/docs',
                    api: '/api/'
                }
            });
        });
        }

        // Health check (sem autenticação)
        targetApp.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                service: 'StonePlugins License API'
            });
        });

        // Ping endpoint para UptimeRobot
        targetApp.get('/ping', (req, res) => {
            res.status(200).send('pong');
        });

        // Status detalhado
        targetApp.get('/status', (req, res) => {
            const memUsage = process.memoryUsage();
            
            res.json({
                status: 'running',
                uptime: process.uptime(),
                memory: {
                    used: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
                    total: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
                    external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
                },
                cpu: process.cpuUsage(),
                platform: process.platform,
                nodeVersion: process.version,
                timestamp: new Date().toISOString()
            });
        });

        // Keep-alive endpoint para evitar sleep no Render
        targetApp.get('/keep-alive', (req, res) => {
            res.status(200).json({
                message: 'Service is alive',
                timestamp: new Date().toISOString()
            });
        });

        // Validar licença - endpoint principal para o plugin
        targetApp.get('/api/license/validate/:licenseKey', async (req, res) => {
            try {
                const { licenseKey } = req.params;
                const serverIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
                const userAgent = req.headers['user-agent'];

                console.log(`[VALIDATE] License: ${licenseKey} from IP: ${serverIp}`);

                // Buscar licença no banco
                const license = await this.database.getLicense(licenseKey);

                let validationResult = {
                    license_key: licenseKey,
                    valid: false,
                    status: 'invalid',
                    message: 'License not found',
                    timestamp: new Date().toISOString()
                };

                if (license) {
                    const isActive = license.status === 'active';

                    if (isActive) {
                        validationResult = {
                            license_key: licenseKey,
                            valid: true,
                            status: 'active',
                            message: 'License is valid',
                            product: {
                                id: license.product_id,
                                title: license.product_title
                            },
                            user: {
                                id: license.user_id,
                                username: license.username
                            },
                            expires_at: license.expires_at,
                            activated_at: license.activated_at,
                            server_ip: license.server_ip,
                            validation_count: license.validation_count + 1,
                            timestamp: new Date().toISOString()
                        };

                        // Atualizar contadores de validação
                        await this.database.updateLicenseValidation(licenseKey, serverIp);
                        console.log(`[VALIDATE] ✅ Valid license: ${licenseKey}`);
                    } else {
                        validationResult.status = 'inactive';
                        validationResult.message = 'License is inactive';
                        console.log(`[VALIDATE] ❌ Inactive license: ${licenseKey}`);
                    }
                } else {
                    console.log(`[VALIDATE] ❌ License not found: ${licenseKey}`);
                }

                // Log da validação
                await this.database.logLicenseValidation({
                    license_key: licenseKey,
                    server_ip: serverIp,
                    user_agent: userAgent,
                    validation_result: validationResult.status
                });

                res.json(validationResult);

            } catch (error) {
                console.error('Error validating license:', error);
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error validating license'
                });
            }
        });

        // Obter informações detalhadas da licença
        targetApp.get('/api/license/info/:licenseKey', async (req, res) => {
            try {
                const { licenseKey } = req.params;
                
                const license = await this.database.getLicense(licenseKey);
                
                if (!license) {
                    return res.status(404).json({
                        error: 'Not Found',
                        message: 'License not found'
                    });
                }

                res.json({
                    license_key: license.license_key,
                    status: license.status,
                    product: {
                        id: license.product_id,
                        title: license.product_title
                    },
                    user: {
                        id: license.user_id,
                        username: license.username
                    },
                    created_at: license.created_at,
                    expires_at: license.expires_at,
                    activated_at: license.activated_at,
                    server_ip: license.server_ip,
                    server_port: license.server_port,
                    last_validation: license.last_validation,
                    validation_count: license.validation_count
                });

            } catch (error) {
                console.error('Error getting license info:', error);
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error getting license info'
                });
            }
        });

        // Ativar licença em um servidor
        targetApp.post('/api/license/activate', async (req, res) => {
            try {
                const { license_key, server_ip, server_port, server_name } = req.body;

                if (!license_key || !server_ip) {
                    return res.status(400).json({
                        error: 'Bad Request',
                        message: 'license_key and server_ip are required'
                    });
                }

                const license = await this.database.getLicense(license_key);
                
                if (!license) {
                    return res.status(404).json({
                        error: 'Not Found',
                        message: 'License not found'
                    });
                }

                if (license.status !== 'active') {
                    return res.status(400).json({
                        error: 'Bad Request',
                        message: 'License is not active'
                    });
                }

                // Ativar licença
                await this.database.activateLicense(license_key, {
                    server_ip,
                    server_port,
                    server_name
                });

                console.log(`[ACTIVATE] License ${license_key} activated on ${server_ip}:${server_port}`);

                res.json({
                    success: true,
                    message: 'License activated successfully',
                    license_key,
                    server_ip,
                    activated_at: new Date().toISOString()
                });

            } catch (error) {
                console.error('Error activating license:', error);
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error activating license'
                });
            }
        });

        // Listar todas as licenças (para administração)
        targetApp.get('/api/licenses', async (req, res) => {
            try {
                // Verificar se tem permissão de admin
                if (!req.apiKey.permissions.includes('admin')) {
                    return res.status(403).json({
                        error: 'Forbidden',
                        message: 'Admin permissions required'
                    });
                }

                const page = parseInt(req.query.page) || 1;
                const limit = parseInt(req.query.limit) || 50;
                const offset = (page - 1) * limit;

                const licenses = await this.getAllLicenses();
                
                res.json({
                    licenses: licenses.slice(offset, offset + limit),
                    pagination: {
                        page,
                        limit,
                        total: licenses.length,
                        pages: Math.ceil(licenses.length / limit)
                    }
                });

            } catch (error) {
                console.error('Error getting licenses:', error);
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error getting licenses'
                });
            }
        });

        // Estatísticas da API
        targetApp.get('/api/stats', async (req, res) => {
            try {
                if (!req.apiKey.permissions.includes('admin')) {
                    return res.status(403).json({
                        error: 'Forbidden',
                        message: 'Admin permissions required'
                    });
                }

                const salesStats = await this.database.getStats();
                const licenseStats = await this.database.getLicenseStats();

                res.json({
                    sales: salesStats,
                    licenses: licenseStats,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('Error getting stats:', error);
                res.status(500).json({
                    error: 'Internal Server Error',
                    message: 'Error getting stats'
                });
            }
        });

        // Supabase API routes
        const supabaseRoutes = require('./supabaseLicenseAPI');
        this.app.use('/api/supabase', supabaseRoutes);

        // Error handler
        this.app.use((err, req, res, next) => {
            console.error('API Error:', err);
            res.status(500).json({
                error: 'Internal Server Error',
                message: 'Something went wrong'
            });
        });
    }

    async getAllLicenses() {
        return new Promise((resolve, reject) => {
            this.database.db.all(
                `SELECT l.*, p.title as product_title, s.username 
                 FROM licenses l 
                 JOIN products p ON l.product_id = p.id 
                 JOIN sales s ON l.sale_id = s.id 
                 ORDER BY l.created_at DESC`,
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });
    }

    start(port = 3000) {
        return new Promise((resolve, reject) => {
            try {
                this.server = this.app.listen(port, '0.0.0.0', () => {
                    console.log(`🚀 License API running on port ${port}`);
                    console.log(`📋 Health check: http://localhost:${port}/health`);
                    console.log(`🌐 License Manager: http://localhost:${port}/`);
                    console.log(`🔑 API endpoints: http://localhost:${port}/api/`);
                    resolve();
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    stop() {
        if (this.server) {
            this.server.close();
        }
    }
}

module.exports = LicenseAPI;
