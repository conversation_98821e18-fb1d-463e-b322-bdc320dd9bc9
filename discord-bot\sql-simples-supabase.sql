-- SQL SIMPLES E GARANTIDO para Supabase
-- Co<PERSON> e cole no SQL Editor do Supabase

CREATE TABLE published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE product_configs (
    id SERIAL PRIMARY KEY,
    product_id TEXT NOT NULL,
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, config_key)
);

CREATE INDEX idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX idx_product_configs_product_id ON product_configs(product_id);