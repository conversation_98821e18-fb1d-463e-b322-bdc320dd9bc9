const { SupabaseLicenseManager } = require('./supabase');
const fs = require('fs');
const path = require('path');

async function createPublishedMessagesTable() {
    try {
        console.log('🔄 Criando tabela published_messages no Supabase...');
        
        const supabaseLicenseManager = new SupabaseLicenseManager();
        
        // Ler o arquivo SQL
        const sqlPath = path.join(__dirname, 'create-published-messages-table.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        // Executar o SQL no Supabase
        const { data, error } = await supabaseLicenseManager.supabase.rpc('exec_sql', { sql_query: sql });
        
        if (error) {
            console.error('❌ Erro ao criar tabela:', error);
            // Tentar método alternativo
            console.log('🔄 Tentando método alternativo...');
            
            const { data: tableData, error: tableError } = await supabaseLicenseManager.supabase
                .from('published_messages')
                .select('*')
                .limit(1);
                
            if (tableError && tableError.code === '42P01') {
                console.log('📋 Tabela não existe, criando manualmente...');
                console.log('Execute o seguinte SQL no painel do Supabase:');
                console.log('----------------------------------------');
                console.log(sql);
                console.log('----------------------------------------');
            } else {
                console.log('✅ Tabela published_messages já existe!');
            }
        } else {
            console.log('✅ Tabela published_messages criada com sucesso!');
        }
        
    } catch (error) {
        console.error('❌ Erro durante a migração:', error);
        console.log('📋 Execute manualmente o SQL no painel do Supabase:');
        console.log('----------------------------------------');
        const sqlPath = path.join(__dirname, 'create-published-messages-table.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        console.log(sql);
        console.log('----------------------------------------');
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    createPublishedMessagesTable()
        .then(() => {
            console.log('🎉 Migração concluída!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Erro na migração:', error);
            process.exit(1);
        });
}

module.exports = { createPublishedMessagesTable };