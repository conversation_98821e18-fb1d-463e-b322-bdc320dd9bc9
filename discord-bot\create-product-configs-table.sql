-- Criar tabela para configurações de produtos
CREATE TABLE IF NOT EXISTS product_configs (
    id SERIAL PRIMARY KEY,
    product_id TEXT NOT NULL,
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, config_key)
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_product_configs_product_id ON product_configs(product_id);
CREATE INDEX IF NOT EXISTS idx_product_configs_key ON product_configs(config_key);

-- Comentários para documentação
COMMENT ON TABLE product_configs IS 'Configurações específicas de cada produto (canal de changelog, cargo de anúncios, etc.)';
COMMENT ON COLUMN product_configs.product_id IS 'ID do produto';
COMMENT ON COLUMN product_configs.config_key IS 'Chave da configuração (ex: changelog_channel_id, announcement_role_id)';
COMMENT ON COLUMN product_configs.config_value IS 'Valor da configuração';

-- Exemplos de configurações:
-- INSERT INTO product_configs (product_id, config_key, config_value) VALUES ('produto-123', 'changelog_channel_id', '1234567890');
-- INSERT INTO product_configs (product_id, config_key, config_value) VALUES ('produto-123', 'announcement_role_id', '0987654321');