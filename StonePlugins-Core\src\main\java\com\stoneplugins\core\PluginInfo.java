package com.stoneplugins.core;

/**
 * Classe para armazenar informações de plugins registrados
 */
public class PluginInfo {
    private String pluginName;
    private String version;
    private String requiredLicense;
    private boolean isActive;
    private String lastCheck;
    
    public PluginInfo(String pluginName, String version, String requiredLicense) {
        this.pluginName = pluginName;
        this.version = version;
        this.requiredLicense = requiredLicense;
        this.isActive = false;
        this.lastCheck = null;
    }
    
    // Getters
    public String getPluginName() { return pluginName; }
    public String getVersion() { return version; }
    public String getRequiredLicense() { return requiredLicense; }
    public boolean isActive() { return isActive; }
    public String getLastCheck() { return lastCheck; }
    
    // Setters
    public void setActive(boolean active) { this.isActive = active; }
    public void setLastCheck(String lastCheck) { this.lastCheck = lastCheck; }
    public void setVersion(String version) { this.version = version; }
}