# This is the main configuration file for PaperSpigot.
# As you can see, there's tons to configure. Some options may impact gameplay, so use
# with caution, and make sure you know what each option does before configuring.
# 
# If you need help with the configuration or have any questions related to PaperSpigot,
# join us at the IRC.
# 
# IRC: #paperspigot @ irc.spi.gt ( http://irc.spi.gt/iris/?channels=PaperSpigot )

config-version: 9
warnWhenSettingExcessiveVelocity: true
effect-modifiers:
  strength: 1.3
  weakness: -0.5
stackable-buckets:
  lava: false
  water: false
  milk: false
settings:
  baby-zombie-movement-speed: 0.5
  limit-player-interactions: true
data-value-allowed-items: []
world-settings:
  default:
    verbose: true
    water-over-lava-flow-speed: 5
    tnt-entity-height-nerf: 0
    use-hopper-check: false
    fix-cannons: false
    disable-explosion-knockback: false
    all-chunks-are-slime-chunks: false
    allow-undead-horse-leashing: false
    container-update-tick-rate: 1
    game-mechanics:
      disable-end-credits: false
      boats-drop-boats: false
      disable-player-crits: false
      disable-chest-cat-detection: false
    keep-spawn-loaded: true
    falling-blocks-collide-with-signs: false
    disable-mood-sounds: false
    portal-search-radius: 128
    disable-ice-and-snow: false
    use-async-lighting: false
    tick-next-tick-list-cap: 10000
    tick-next-tick-list-cap-ignores-redstone: false
    disable-thunder: false
    generator-settings:
      canyon: true
      caves: true
      dungeon: true
      fortress: true
      mineshaft: true
      monument: true
      stronghold: true
      temple: true
      village: true
      flat-bedrock: false
    allow-block-location-tab-completion: true
    disable-teleportation-suffocation-check: false
    remove-invalid-mob-spawner-tile-entities: true
    player-blocking-damage-multiplier: 0.5
    optimize-explosions: false
    mob-spawner-tick-rate: 1
    max-growth-height:
      cactus: 3
      reeds: 3
    tnt-explosion-volume: 4.0
    nether-ceiling-void-damage: false
    falling-block-height-nerf: 0
    cache-chunk-maps: false
    fast-drain:
      lava: false
      water: false
    lava-flow-speed:
      normal: 30
      nether: 10
    player-exhaustion:
      block-break: 0.02500000037252903
      swimming: 0.014999999664723873
    load-chunks:
      enderpearls: false
      tnt-entities: false
      falling-blocks: false
    squid-spawn-height:
      minimum: 45.0
      maximum: 63.0
    remove-unloaded:
      enderpearls: true
      tnt-entities: true
      falling-blocks: true
    fishing-time-range:
      MinimumTicks: 100
      MaximumTicks: 900
    despawn-ranges:
      soft: 32
      hard: 128
