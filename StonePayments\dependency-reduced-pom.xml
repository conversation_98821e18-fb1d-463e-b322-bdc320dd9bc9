<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.stoneplugins</groupId>
  <artifactId>stonepayments</artifactId>
  <name>StonePayments</name>
  <version>1.0.0</version>
  <description>Sistema de Pagamento Inovador via Pix no Minecraft</description>
  <build>
    <finalName>stonepayments-${project.version}</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>okhttp3</pattern>
                  <shadedPattern>com.stoneplugins.stonepayments.libs.okhttp3</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>okio</pattern>
                  <shadedPattern>com.stoneplugins.stonepayments.libs.okio</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.google.gson</pattern>
                  <shadedPattern>com.stoneplugins.stonepayments.libs.gson</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.google.zxing</pattern>
                  <shadedPattern>com.stoneplugins.stonepayments.libs.zxing</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.sqlite</pattern>
                  <shadedPattern>com.stoneplugins.stonepayments.libs.sqlite</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <echo />
                <delete />
                <copy />
                <echo />
                <echo />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
    <repository>
      <id>central</id>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.8.8-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.MilkBowl</groupId>
      <artifactId>VaultAPI</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.stoneplugins</groupId>
      <artifactId>stoneplugins-core</artifactId>
      <version>1.0.0</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.target>8</maven.compiler.target>
    <maven.compiler.source>8</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
