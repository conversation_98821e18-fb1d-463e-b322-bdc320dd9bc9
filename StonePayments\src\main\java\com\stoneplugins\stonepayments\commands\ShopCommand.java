package com.stoneplugins.stonepayments.commands;

import com.stoneplugins.stonepayments.StonePayments;
import com.stoneplugins.stonepayments.menus.ShopMenu;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class ShopCommand implements CommandExecutor {

    private final StonePayments plugin;

    public ShopCommand(StonePayments plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c[StonePayments] Este comando só pode ser usado por jogadores.");
            return true;
        }

        Player player = (Player) sender;

        // Verificar se funcionalidade requer licença
        if (plugin.requiresLicense("payment")) {
            player.sendMessage("§c[StonePayments] Esta funcionalidade requer licença ativa!");
            player.sendMessage("§e[StonePayments] Use: §f/stoneplugins ativar <licença>");
            player.sendMessage("§e[StonePayments] Contate o administrador para ativar a licença.");
            return true;
        }

        // Verificar se o sistema está inicializado
        if (!plugin.isLicenseActive()) {
            player.sendMessage("§c[StonePayments] Sistema de pagamentos não está disponível!");
            player.sendMessage("§e[StonePayments] Contate o administrador.");
            return true;
        }

        try {
            // Abrir menu da loja
            ShopMenu shopMenu = new ShopMenu(plugin);
            shopMenu.open(player);
            
        } catch (Exception e) {
            player.sendMessage("§c[StonePayments] Erro ao abrir a loja: " + e.getMessage());
            plugin.getLogger().severe("Erro ao abrir menu para " + player.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }
}