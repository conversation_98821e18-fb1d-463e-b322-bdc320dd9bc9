const { SupabaseLicenseManager } = require('./supabase');

async function setupSupabaseTables() {
    try {
        console.log('🔧 Configurando tabelas do Supabase...');
        
        const supabaseLicenseManager = new SupabaseLicenseManager();
        
        // SQL para criar as tabelas
        const createTablesSQL = `
            -- Criar tabela para mensagens publicadas (se não existir)
            CREATE TABLE IF NOT EXISTS published_messages (
                id SERIAL PRIMARY KEY,
                message_id TEXT NOT NULL UNIQUE,
                channel_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                guild_id TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Criar tabela para configurações de produtos
            CREATE TABLE IF NOT EXISTS product_configs (
                id SERIAL PRIMARY KEY,
                product_id TEXT NOT NULL,
                config_key TEXT NOT NULL,
                config_value TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(product_id, config_key)
            );

            -- Criar índices
            CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
            CREATE INDEX IF NOT EXISTS idx_product_configs_product_id ON product_configs(product_id);
        `;

        // Tentar executar o SQL
        try {
            const { data, error } = await supabaseLicenseManager.supabase.rpc('exec_sql', { 
                sql_query: createTablesSQL 
            });
            
            if (error) {
                console.error('❌ Erro ao executar SQL via RPC:', error);
                throw error;
            }
            
            console.log('✅ Tabelas criadas via RPC com sucesso!');
            
        } catch (rpcError) {
            console.log('⚠️ RPC não disponível, tentando método alternativo...');
            
            // Método alternativo: tentar criar tabelas uma por uma
            await createTableAlternative(supabaseLicenseManager);
        }
        
        // Testar se as tabelas foram criadas
        await testTables(supabaseLicenseManager);
        
        console.log('🎉 Configuração do Supabase concluída!');
        
    } catch (error) {
        console.error('❌ Erro durante a configuração:', error);
        console.log('📋 Execute manualmente o SQL no painel do Supabase:');
        console.log('----------------------------------------');
        const fs = require('fs');
        const path = require('path');
        const sqlPath = path.join(__dirname, 'create-all-missing-tables.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        console.log(sql);
        console.log('----------------------------------------');
    }
}

async function createTableAlternative(supabaseLicenseManager) {
    console.log('🔧 Tentando criar tabelas individualmente...');
    
    // Tentar criar published_messages
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('published_messages')
            .select('*')
            .limit(1);
            
        if (error && error.code === '42P01') {
            console.log('📋 Tabela published_messages não existe - precisa ser criada manualmente');
        } else {
            console.log('✅ Tabela published_messages já existe');
        }
    } catch (error) {
        console.log('⚠️ Erro ao verificar published_messages:', error.message);
    }
    
    // Tentar criar product_configs
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('product_configs')
            .select('*')
            .limit(1);
            
        if (error && error.code === '42P01') {
            console.log('📋 Tabela product_configs não existe - precisa ser criada manualmente');
        } else {
            console.log('✅ Tabela product_configs já existe');
        }
    } catch (error) {
        console.log('⚠️ Erro ao verificar product_configs:', error.message);
    }
}

async function testTables(supabaseLicenseManager) {
    console.log('🧪 Testando tabelas criadas...');
    
    // Testar published_messages
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('published_messages')
            .select('count(*)')
            .single();
            
        if (!error) {
            console.log('✅ Tabela published_messages funcionando');
        }
    } catch (error) {
        console.log('❌ Tabela published_messages não está funcionando:', error.message);
    }
    
    // Testar product_configs
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('product_configs')
            .select('count(*)')
            .single();
            
        if (!error) {
            console.log('✅ Tabela product_configs funcionando');
        }
    } catch (error) {
        console.log('❌ Tabela product_configs não está funcionando:', error.message);
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    setupSupabaseTables()
        .then(() => {
            console.log('✅ Setup finalizado');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Erro no setup:', error);
            process.exit(1);
        });
}

module.exports = { setupSupabaseTables };