@echo off
echo ========================================
echo    Testando Plugins StonePlugins
echo ========================================

echo.
echo Verificando arquivos...
echo.

if exist "servidor\plugins\StonePluginsDepend.jar" (
    echo ✅ StonePluginsDepend.jar encontrado
    for %%I in (servidor\plugins\StonePluginsDepend.jar) do echo    Tamanho: %%~zI bytes
) else (
    echo ❌ StonePluginsDepend.jar NAO encontrado!
)

if exist "servidor\plugins\stonepayments-1.0.0.jar" (
    echo ✅ stonepayments-1.0.0.jar encontrado
    for %%I in (servidor\plugins\stonepayments-1.0.0.jar) do echo    Tamanho: %%~zI bytes
) else (
    echo ❌ stonepayments-1.0.0.jar NAO encontrado!
)

echo.
echo Verificando conteudo do StonePluginsDepend.jar...
echo.

REM Usar PowerShell para verificar conteúdo do JAR
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; $jar = [System.IO.Compression.ZipFile]::OpenRead('servidor\plugins\StonePluginsDepend.jar'); Write-Host 'Arquivos no JAR:'; $jar.Entries | Where-Object { $_.Name -match '\.(yml|yaml|properties)$' } | ForEach-Object { Write-Host '  ' $_.FullName }; $jar.Dispose()"

echo.
echo ========================================
echo    Verificacao Concluida
echo ========================================

pause