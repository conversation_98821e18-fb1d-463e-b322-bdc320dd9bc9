package com.stoneplugins.stonepayments.commands;

import com.stoneplugins.stonepayments.StonePayments;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class PaymentCommand implements CommandExecutor {

    private final StonePayments plugin;

    public PaymentCommand(StonePayments plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        if (args.length == 0) {
            showHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                if (!sender.hasPermission("stonepayments.admin")) {
                    sender.sendMessage("§c[StonePayments] Você não tem permissão para isso!");
                    return true;
                }
                reloadPlugin(sender);
                break;

            case "status":
                if (!sender.hasPermission("stonepayments.admin")) {
                    sender.sendMessage("§c[StonePayments] Você não tem permissão para isso!");
                    return true;
                }
                showStatus(sender);
                break;

            case "license":
                showLicenseInfo(sender);
                break;

            case "info":
                showInfo(sender);
                break;

            default:
                showHelp(sender);
                break;
        }

        return true;
    }

    private void showHelp(CommandSender sender) {
        sender.sendMessage("§6§l[StonePayments] §fComandos disponíveis:");
        sender.sendMessage("§e/comprar §7- Abrir loja de pagamentos");
        sender.sendMessage("§e/stonepayments info §7- Informações do plugin");
        sender.sendMessage("§e/stonepayments license §7- Status da licença");
        
        if (sender.hasPermission("stonepayments.admin")) {
            sender.sendMessage("§c§lComandos Administrativos:");
            sender.sendMessage("§e/stonepayments reload §7- Recarregar configurações");
            sender.sendMessage("§e/stonepayments status §7- Status do sistema");
        }
    }

    private void showInfo(CommandSender sender) {
        sender.sendMessage("§6§l[StonePayments] §fInformações:");
        sender.sendMessage("§7Versão: §f" + plugin.getDescription().getVersion());
        sender.sendMessage("§7Desenvolvido por: §fStonePlugins");
        sender.sendMessage("§7Sistema: §fPagamentos via PIX");
        sender.sendMessage("§7Licença: " + (plugin.isLicenseActive() ? "§aAtiva" : "§cInativa"));
        sender.sendMessage("§7Comando principal: §f/comprar");
    }

    private void showLicenseInfo(CommandSender sender) {
        sender.sendMessage("§6§l[StonePayments] §fStatus da Licença:");
        if (plugin.isLicenseActive()) {
            sender.sendMessage("§a Licença ativa - Sistema de pagamentos habilitado!");
            sender.sendMessage("§a[StonePayments] Todas as funcionalidades disponíveis.");
        } else {
            sender.sendMessage("§c Licença não encontrada!");
            sender.sendMessage("§e[StonePayments] Para ativar sua licença:");
            sender.sendMessage("§e[StonePayments] 1. Use: §f/stoneplugins ativar <sua-licença>");
            sender.sendMessage("§e[StonePayments] 2. Configure o access_token em config.yml");
            sender.sendMessage("§e[StonePayments] 3. Reinicie o plugin ou servidor");
            sender.sendMessage("§e[StonePayments] 4. Use §f/comprar §epara acessar a loja");
        }
    }

    private void showStatus(CommandSender sender) {
        sender.sendMessage("§6§l[StonePayments] §fStatus do Sistema:");
        sender.sendMessage("§7Plugin: " + (plugin.isEnabled() ? "§aHabilitado" : "§cDesabilitado"));
        sender.sendMessage("§7Licença: " + (plugin.isLicenseActive() ? "§aAtiva" : "§cInativa"));
        
        if (plugin.getMercadoPagoAPI() != null) {
            sender.sendMessage("§7Mercado Pago: " + (plugin.getMercadoPagoAPI().isTokenValid() ? "§aConectado" : "§cDesconectado"));
        } else {
            sender.sendMessage("§7Mercado Pago: §cNão inicializado");
        }
        
        if (plugin.getDatabaseManager() != null) {
            sender.sendMessage("§7Banco de Dados: §aConectado");
        } else {
            sender.sendMessage("§7Banco de Dados: §cDesconectado");
        }
    }

    private void reloadPlugin(CommandSender sender) {
        sender.sendMessage("§e[StonePayments] Recarregando configurações...");
        
        try {
            // Recarregar configurações
            plugin.getConfigManager().loadConfigs();
            
            sender.sendMessage("§a[StonePayments] Configurações recarregadas com sucesso!");
            
        } catch (Exception e) {
            sender.sendMessage("§c[StonePayments] Erro ao recarregar: " + e.getMessage());
            plugin.getLogger().severe("Erro ao recarregar configurações: " + e.getMessage());
        }
    }
}