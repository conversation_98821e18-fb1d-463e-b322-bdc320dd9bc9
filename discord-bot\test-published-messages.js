const { SupabaseLicenseManager } = require('./supabase');

async function testPublishedMessages() {
    try {
        console.log('🧪 Testando sistema de mensagens publicadas...');
        
        const supabaseLicenseManager = new SupabaseLicenseManager();
        
        // Teste 1: Verificar se a tabela existe
        console.log('📋 Teste 1: Verificando se a tabela published_messages existe...');
        try {
            const { data, error } = await supabaseLicenseManager.supabase
                .from('published_messages')
                .select('*')
                .limit(1);
                
            if (error) {
                console.error('❌ Erro ao acessar tabela published_messages:', error);
                console.log('💡 A tabela provavelmente não existe. Execute o SQL no painel do Supabase:');
                console.log(`
CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_channel_id ON published_messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_guild_id ON published_messages(guild_id);
                `);
                return;
            } else {
                console.log('✅ Tabela published_messages existe e está acessível');
            }
        } catch (tableError) {
            console.error('❌ Erro ao verificar tabela:', tableError);
            return;
        }
        
        // Teste 2: Buscar produtos existentes
        console.log('📋 Teste 2: Buscando produtos existentes...');
        try {
            const products = await supabaseLicenseManager.getAllProducts();
            console.log(`✅ Encontrados ${products.length} produtos`);
            
            if (products.length > 0) {
                const firstProduct = products[0];
                console.log(`📦 Primeiro produto: ${firstProduct.name} (ID: ${firstProduct.id})`);
                
                // Teste 3: Buscar mensagens publicadas deste produto
                console.log('📋 Teste 3: Buscando mensagens publicadas...');
                const publishedMessages = await supabaseLicenseManager.getPublishedMessages(firstProduct.id);
                console.log(`📨 Encontradas ${publishedMessages.length} mensagens publicadas para este produto`);
                
                if (publishedMessages.length > 0) {
                    console.log('📨 Mensagens encontradas:');
                    publishedMessages.forEach((msg, index) => {
                        console.log(`  ${index + 1}. Message ID: ${msg.message_id}, Channel: ${msg.channel_id}`);
                    });
                } else {
                    console.log('ℹ️ Nenhuma mensagem publicada encontrada. Isso é normal se você ainda não publicou produtos.');
                }
            } else {
                console.log('ℹ️ Nenhum produto encontrado. Crie um produto primeiro.');
            }
        } catch (productError) {
            console.error('❌ Erro ao buscar produtos:', productError);
        }
        
        console.log('🎉 Teste concluído!');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    testPublishedMessages()
        .then(() => {
            console.log('✅ Teste finalizado');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Erro no teste:', error);
            process.exit(1);
        });
}

module.exports = { testPublishedMessages };