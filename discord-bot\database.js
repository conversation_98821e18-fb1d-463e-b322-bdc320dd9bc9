const path = require('path');

class Database {
    constructor() {
        // Detectar se está no Render (PostgreSQL) ou local (SQLite)
        if (process.env.DATABASE_URL) {
            // PostgreSQL no Render
            const { Pool } = require('pg');
            this.isPostgres = true;
            this.db = new Pool({
                connectionString: process.env.DATABASE_URL,
                ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
            });
            console.log('🐘 Usando PostgreSQL (Render)');
        } else {
            // SQLite local - só carregar se não estiver no Render
            try {
                const sqlite3 = require('sqlite3').verbose();
                this.isPostgres = false;
                this.db = new sqlite3.Database(process.env.DATABASE_PATH || './database.sqlite');
                console.log('💾 Usando SQLite (Local)');
            } catch (error) {
                console.log('⚠️ SQLite não disponível - usando apenas Supabase');
                this.isPostgres = false;
                this.db = null;
            }
        }
        this.init();
    }

    async init() {
        try {
            if (this.isPostgres) {
                await this.initPostgres();
            } else if (this.db) {
                await this.initSQLite();
            } else {
                console.log('⚠️ Nenhum banco de dados local disponível - usando apenas Supabase');
            }
            console.log('✅ Database initialized successfully');
        } catch (error) {
            console.error('❌ Erro ao inicializar database:', error);
        }
    }

    async initPostgres() {
        // Criar tabelas PostgreSQL
        const queries = [
            `CREATE TABLE IF NOT EXISTS products (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                version TEXT DEFAULT 'v1.0.0',
                download_link TEXT,
                button_text TEXT DEFAULT 'Comprar Agora',
                button_color TEXT DEFAULT 'Primary',
                price DECIMAL NOT NULL,
                stock INTEGER DEFAULT -1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )`,
            
            `CREATE TABLE IF NOT EXISTS sales (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                username TEXT NOT NULL,
                product_id TEXT NOT NULL,
                amount DECIMAL NOT NULL,
                payment_id TEXT,
                status TEXT DEFAULT 'pending',
                channel_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP
            )`,
            
            `CREATE TABLE IF NOT EXISTS api_keys (
                id TEXT PRIMARY KEY,
                key_name TEXT NOT NULL,
                api_key TEXT UNIQUE NOT NULL,
                permissions TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                last_used TIMESTAMP,
                status TEXT DEFAULT 'active'
            )`,
            
            `CREATE TABLE IF NOT EXISTS published_messages (
                id SERIAL PRIMARY KEY,
                message_id TEXT NOT NULL,
                channel_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                guild_id TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(message_id)
            )`,
            
            `CREATE TABLE IF NOT EXISTS product_configs (
                id SERIAL PRIMARY KEY,
                product_id TEXT NOT NULL,
                config_key TEXT NOT NULL,
                config_value TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(product_id, config_key)
            )`
        ];

        for (const query of queries) {
            await this.db.query(query);
        }
    }

    async initSQLite() {
        return new Promise((resolve, reject) => {
            const queries = [
                `CREATE TABLE IF NOT EXISTS products (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    version TEXT DEFAULT 'v1.0.0',
                    download_link TEXT,
                    button_text TEXT DEFAULT 'Comprar Agora',
                    button_color TEXT DEFAULT 'Primary',
                    price REAL NOT NULL,
                    stock INTEGER DEFAULT -1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )`,
                
                `CREATE TABLE IF NOT EXISTS sales (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    username TEXT NOT NULL,
                    product_id TEXT NOT NULL,
                    amount REAL NOT NULL,
                    payment_id TEXT,
                    status TEXT DEFAULT 'pending',
                    channel_id TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME
                )`,
                
                `CREATE TABLE IF NOT EXISTS api_keys (
                    id TEXT PRIMARY KEY,
                    key_name TEXT NOT NULL,
                    api_key TEXT UNIQUE NOT NULL,
                    permissions TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    last_used DATETIME,
                    status TEXT DEFAULT 'active'
                )`,
                
                `CREATE TABLE IF NOT EXISTS published_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message_id TEXT NOT NULL,
                    channel_id TEXT NOT NULL,
                    product_id TEXT NOT NULL,
                    guild_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(message_id)
                )`,
                
                `CREATE TABLE IF NOT EXISTS product_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id TEXT NOT NULL,
                    config_key TEXT NOT NULL,
                    config_value TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(product_id, config_key)
                )`
            ];

            let completed = 0;
            queries.forEach(query => {
                this.db.run(query, (err) => {
                    if (err) reject(err);
                    completed++;
                    if (completed === queries.length) {
                        // Após criar tabelas, executar migrações se necessário
                        this.runMigrations().then(() => resolve()).catch(reject);
                    }
                });
            });
        });
    }

    async runMigrations() {
        try {
            // Verificar se existem produtos sem download_link
            const productsWithoutLink = await this.query('SELECT COUNT(*) as count FROM products WHERE download_link IS NULL OR download_link = ""');
            if (productsWithoutLink[0].count > 0) {
                console.log(`⚠️ Encontrados ${productsWithoutLink[0].count} produtos sem download_link`);
            }
        } catch (error) {
            console.error('Erro ao executar migrações:', error);
        }
    }

    // Método universal para executar queries
    async query(sql, params = []) {
        if (this.isPostgres) {
            const result = await this.db.query(sql, params);
            return result.rows;
        } else if (this.db) {
            return new Promise((resolve, reject) => {
                this.db.all(sql, params, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                });
            });
        } else {
            console.log('⚠️ Banco de dados não disponível - retornando array vazio');
            return [];
        }
    }

    async run(sql, params = []) {
        if (this.isPostgres) {
            const result = await this.db.query(sql, params);
            return { changes: result.rowCount, lastID: result.insertId };
        } else if (this.db) {
            return new Promise((resolve, reject) => {
                this.db.run(sql, params, function(err) {
                    if (err) reject(err);
                    else resolve({ changes: this.changes, lastID: this.lastID });
                });
            });
        } else {
            console.log('⚠️ Banco de dados não disponível - operação ignorada');
            return { changes: 0, lastID: null };
        }
    }

    async get(sql, params = []) {
        if (this.isPostgres) {
            const result = await this.db.query(sql, params);
            return result.rows[0] || null;
        } else if (this.db) {
            return new Promise((resolve, reject) => {
                this.db.get(sql, params, (err, row) => {
                    if (err) reject(err);
                    else resolve(row || null);
                });
            });
        } else {
            console.log('⚠️ Banco de dados não disponível - retornando null');
            return null;
        }
    }

    // Produtos
    async createProduct(productData) {
        const { id, title, description, version, download_link, button_text, button_color, price, stock } = productData;

        console.log('💾 Criando produto no banco de dados:');
        console.log('💾 ID:', id);
        console.log('💾 Title:', title);
        console.log('💾 Download Link:', download_link);
        console.log('💾 Version:', version);

        const result = await this.run(
            `INSERT INTO products (id, title, description, version, download_link, button_text, button_color, price, stock)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [id, title, description, version, download_link, button_text, button_color, price, stock]
        );

        console.log('✅ Produto criado no banco com ID:', result.lastID || id);
        return result.lastID;
    }

    async getProduct(id) {
        console.log(`🔍 Buscando produto no banco com ID: ${id}`);
        const product = await this.get('SELECT * FROM products WHERE id = ?', [id]);
        console.log(`🔍 Produto encontrado:`, product ? {
            id: product.id,
            title: product.title,
            download_link: product.download_link,
            version: product.version
        } : 'null');
        return product;
    }

    async getAllProducts() {
        return await this.query('SELECT * FROM products ORDER BY created_at DESC');
    }

    async updateProduct(id, productData) {
        const { title, description, version, download_link, button_text, price } = productData;

        // Construir query dinamicamente baseado nos campos fornecidos
        const fields = [];
        const values = [];

        if (title !== undefined) { fields.push('title = ?'); values.push(title); }
        if (description !== undefined) { fields.push('description = ?'); values.push(description); }
        if (version !== undefined) { fields.push('version = ?'); values.push(version); }
        if (download_link !== undefined) { fields.push('download_link = ?'); values.push(download_link); }
        if (button_text !== undefined) { fields.push('button_text = ?'); values.push(button_text); }
        if (price !== undefined) { fields.push('price = ?'); values.push(price); }

        if (fields.length === 0) return 0;

        fields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const result = await this.run(
            `UPDATE products SET ${fields.join(', ')} WHERE id = ?`,
            values
        );
        return result.changes;
    }

    async deleteProduct(id) {
        const result = await this.run('DELETE FROM products WHERE id = ?', [id]);
        return result.changes;
    }

    async updateProductStock(id, newStock) {
        const result = await this.run('UPDATE products SET stock = ? WHERE id = ?', [newStock, id]);
        return result.changes;
    }

    // Vendas
    async createSale(saleData) {
        const { id, user_id, username, product_id, amount, channel_id } = saleData;
        const result = await this.run(
            `INSERT INTO sales (id, user_id, username, product_id, amount, channel_id) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [id, user_id, username, product_id, amount, channel_id]
        );
        return result.lastID;
    }

    async updateSaleStatus(id, status, paymentId = null) {
        const query = paymentId 
            ? 'UPDATE sales SET status = ?, payment_id = ?, completed_at = CURRENT_TIMESTAMP WHERE id = ?'
            : 'UPDATE sales SET status = ? WHERE id = ?';
        
        const params = paymentId ? [status, paymentId, id] : [status, id];
        const result = await this.run(query, params);
        return result.changes;
    }

    async getSale(id) {
        return await this.get('SELECT * FROM sales WHERE id = ?', [id]);
    }

    async getSaleByChannelId(channelId) {
        return await this.get('SELECT * FROM sales WHERE channel_id = ?', [channelId]);
    }

    async getPendingSales() {
        return await this.query(
            `SELECT * FROM sales WHERE status IN ('pending', 'awaiting_payment', 'failed') ORDER BY created_at DESC`
        );
    }

    async getAllSales() {
        return await this.query('SELECT * FROM sales ORDER BY created_at DESC');
    }

    // API Keys
    async createApiKey(keyData) {
        const { id, key_name, api_key, permissions, expires_at } = keyData;
        const result = await this.run(
            `INSERT INTO api_keys (id, key_name, api_key, permissions, expires_at) 
             VALUES (?, ?, ?, ?, ?)`,
            [id, key_name, api_key, permissions, expires_at]
        );
        return result.lastID;
    }

    async validateApiKey(apiKey) {
        const row = await this.get(
            `SELECT * FROM api_keys 
             WHERE api_key = ? AND status = 'active' 
             AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)`,
            [apiKey]
        );
        
        if (row) {
            // Atualizar last_used
            await this.run(
                'UPDATE api_keys SET last_used = CURRENT_TIMESTAMP WHERE api_key = ?',
                [apiKey]
            );
        }
        
        return row;
    }

    async getAllApiKeys() {
        return await this.query('SELECT * FROM api_keys ORDER BY created_at DESC');
    }

    // Estatísticas
    async getStats() {
        const result = await this.query(`
            SELECT 
                COUNT(*) as total_sales,
                SUM(amount) as total_revenue,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sales,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_sales
            FROM sales
        `);
        return result[0];
    }

    // Métodos dummy para licenças (já que não usamos mais backup local)
    async createLicense() { return null; }
    async getLicense() { return null; }
    async getAllLicenses() { return []; }
    async getLicensesByUserId() { return []; }
    async updateLicenseValidation() { return 0; }
    async activateLicense() { return null; }
    async logLicenseValidation() { return null; }
    async updateLicenseSync() { return 0; }
    async deleteLicense() { return 0; }
    async deleteAllLicenses() { return 0; }
    async getLicenseStats() { 
        return { 
            total_licenses: 0, 
            active_licenses: 0, 
            expired_licenses: 0, 
            activated_licenses: 0 
        }; 
    }

    close() {
        if (this.isPostgres) {
            this.db.end();
        } else if (this.db) {
            this.db.close();
        } else {
            console.log('⚠️ Nenhum banco de dados para fechar');
        }
    }

    // Verificar se o banco de dados está disponível
    isDatabaseAvailable() {
        return this.isPostgres || this.db !== null;
    }

    // Salvar mensagem publicada
    async savePublishedMessage(messageData) {
        try {
            if (this.isPostgres) {
                await this.db.query(
                    'INSERT INTO published_messages (message_id, channel_id, product_id, guild_id) VALUES ($1, $2, $3, $4) ON CONFLICT (message_id) DO NOTHING',
                    [messageData.message_id, messageData.channel_id, messageData.product_id, messageData.guild_id]
                );
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.run(
                        'INSERT OR IGNORE INTO published_messages (message_id, channel_id, product_id, guild_id) VALUES (?, ?, ?, ?)',
                        [messageData.message_id, messageData.channel_id, messageData.product_id, messageData.guild_id],
                        function(err) {
                            if (err) reject(err);
                            else resolve(this.lastID);
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Erro ao salvar mensagem publicada:', error);
            throw error;
        }
    }

    // Buscar mensagens publicadas de um produto
    async getPublishedMessages(productId) {
        try {
            if (this.isPostgres) {
                const result = await this.db.query(
                    'SELECT * FROM published_messages WHERE product_id = $1 ORDER BY created_at DESC',
                    [productId]
                );
                return result.rows;
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.all(
                        'SELECT * FROM published_messages WHERE product_id = ? ORDER BY created_at DESC',
                        [productId],
                        (err, rows) => {
                            if (err) reject(err);
                            else resolve(rows || []);
                        }
                    );
                });
            }
            return [];
        } catch (error) {
            console.error('Erro ao buscar mensagens publicadas:', error);
            return [];
        }
    }

    // Remover mensagem publicada (quando deletada)
    async removePublishedMessage(messageId) {
        try {
            if (this.isPostgres) {
                await this.db.query('DELETE FROM published_messages WHERE message_id = $1', [messageId]);
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.run(
                        'DELETE FROM published_messages WHERE message_id = ?',
                        [messageId],
                        function(err) {
                            if (err) reject(err);
                            else resolve(this.changes);
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Erro ao remover mensagem publicada:', error);
            throw error;
        }
    }

    // Remover todas as mensagens publicadas de um produto
    async removePublishedMessagesForProduct(productId) {
        try {
            if (this.isPostgres) {
                await this.db.query('DELETE FROM published_messages WHERE product_id = $1', [productId]);
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.run(
                        'DELETE FROM published_messages WHERE product_id = ?',
                        [productId],
                        function(err) {
                            if (err) reject(err);
                            else resolve(this.changes);
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Erro ao remover mensagens publicadas do produto:', error);
            throw error;
        }
    }

    // Salvar configuração do produto
    async saveProductConfig(productId, key, value) {
        try {
            if (this.isPostgres) {
                await this.db.query(
                    'INSERT INTO product_configs (product_id, config_key, config_value) VALUES ($1, $2, $3) ON CONFLICT (product_id, config_key) DO UPDATE SET config_value = $3, updated_at = CURRENT_TIMESTAMP',
                    [productId, key, value]
                );
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.run(
                        'INSERT OR REPLACE INTO product_configs (product_id, config_key, config_value) VALUES (?, ?, ?)',
                        [productId, key, value],
                        function(err) {
                            if (err) reject(err);
                            else resolve(this.lastID);
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Erro ao salvar configuração do produto:', error);
            throw error;
        }
    }

    // Buscar configurações do produto
    async getProductConfig(productId) {
        try {
            if (this.isPostgres) {
                const result = await this.db.query(
                    'SELECT config_key, config_value FROM product_configs WHERE product_id = $1',
                    [productId]
                );
                const config = {};
                result.rows.forEach(row => {
                    config[row.config_key] = row.config_value;
                });
                return config;
            } else if (this.db) {
                return new Promise((resolve, reject) => {
                    this.db.all(
                        'SELECT config_key, config_value FROM product_configs WHERE product_id = ?',
                        [productId],
                        (err, rows) => {
                            if (err) reject(err);
                            else {
                                const config = {};
                                rows.forEach(row => {
                                    config[row.config_key] = row.config_value;
                                });
                                resolve(config);
                            }
                        }
                    );
                });
            }
            return {};
        } catch (error) {
            console.error('Erro ao buscar configurações do produto:', error);
            return {};
        }
    }
}

module.exports = Database;