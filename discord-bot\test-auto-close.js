// Teste simples para verificar o fechamento automático
const { EmbedBuilder } = require('discord.js');

// Simular Utils
const Utils = {
    getEmbedColor: (color) => {
        const colors = {
            'Success': 0x198754,
            'Secondary': 0x6C757D
        };
        return colors[color] || 0x5865F2;
    }
};

// Simular canal e mensagem
const mockChannel = {
    name: 'test-channel',
    id: '123456789',
    deleted: false,
    delete: async (reason) => {
        console.log(`🗑️ Canal deletado: ${reason}`);
        mockChannel.deleted = true;
    }
};

const mockMessage = {
    edit: async (options) => {
        console.log('📝 Mensagem editada:', options.embeds[0].title);
        return true;
    }
};

// Função de teste do fechamento automático
async function startChannelCloseCountdown(channel, message, seconds) {
    console.log(`🔄 [AUTO-CLOSE] Iniciando contagem regressiva de ${seconds} segundos para canal ${channel.name} (ID: ${channel.id})`);
    
    let remainingSeconds = seconds;
    
    const updateCountdown = async () => {
        try {
            // Verificar se o canal ainda existe
            if (!channel || channel.deleted) {
                console.log('⚠️ [AUTO-CLOSE] Canal já foi deletado, cancelando contagem regressiva');
                return;
            }

            if (remainingSeconds <= 0) {
                console.log('🔒 [AUTO-CLOSE] Tempo esgotado, fechando canal...');
                
                // Mostrar mensagem final antes de fechar
                const finalEmbed = new EmbedBuilder()
                    .setTitle('🔒 Fechando Canal')
                    .setDescription('Canal sendo fechado automaticamente...')
                    .setColor(Utils.getEmbedColor('Secondary'))
                    .setTimestamp();

                try {
                    await message.edit({
                        embeds: [finalEmbed]
                    });
                    console.log('✅ [AUTO-CLOSE] Mensagem final enviada');
                } catch (editError) {
                    console.log('⚠️ [AUTO-CLOSE] Erro ao editar mensagem final, prosseguindo com fechamento:', editError.message);
                }

                // Aguardar 3 segundos e fechar o canal
                setTimeout(async () => {
                    try {
                        if (channel && !channel.deleted) {
                            await channel.delete('Pagamento concluído - fechamento automático');
                            console.log(`✅ [AUTO-CLOSE] Canal ${channel.name} fechado automaticamente após pagamento concluído`);
                        } else {
                            console.log('⚠️ [AUTO-CLOSE] Canal já estava deletado');
                        }
                    } catch (error) {
                        console.error('❌ [AUTO-CLOSE] Erro ao deletar canal automaticamente:', error);
                    }
                }, 3000);
                
                return;
            }

            // Atualizar embed com contagem regressiva
            const countdownEmbed = new EmbedBuilder()
                .setTitle('<:check:1399460320854151230> Pagamento Aprovado!')
                .setDescription(`Sua compra foi processada com sucesso!\n\n📧 **Verifique sua DM para receber os dados da licença.**\n\n⏰ **Este canal será fechado automaticamente em ${remainingSeconds} segundos.**`)
                .setColor(Utils.getEmbedColor('Success'))
                .setTimestamp();

            try {
                await message.edit({
                    embeds: [countdownEmbed]
                });
                console.log(`⏰ [AUTO-CLOSE] Contagem regressiva: ${remainingSeconds} segundos restantes`);
            } catch (editError) {
                console.error('❌ [AUTO-CLOSE] Erro ao atualizar contagem regressiva:', editError.message);
                // Se não conseguir editar, continuar a contagem mesmo assim
            }

            remainingSeconds--;
            
            // Agendar próxima atualização
            setTimeout(updateCountdown, 1000);
            
        } catch (error) {
            console.error('❌ [AUTO-CLOSE] Erro geral na contagem regressiva:', error);
            // Se houver erro, tentar fechar o canal mesmo assim após 5 segundos
            setTimeout(async () => {
                try {
                    if (channel && !channel.deleted) {
                        await channel.delete('Erro na contagem regressiva - fechamento forçado');
                        console.log(`✅ [AUTO-CLOSE] Canal ${channel.name} fechado após erro na contagem regressiva`);
                    }
                } catch (deleteError) {
                    console.error('❌ [AUTO-CLOSE] Erro ao deletar canal após erro na contagem:', deleteError);
                }
            }, 5000);
        }
    };

    // Iniciar contagem regressiva após 1 segundo
    console.log('🚀 [AUTO-CLOSE] Iniciando contagem regressiva...');
    setTimeout(updateCountdown, 1000);
}

// Executar teste
console.log('🧪 Testando fechamento automático do canal...');
startChannelCloseCountdown(mockChannel, mockMessage, 5); // Teste com 5 segundos