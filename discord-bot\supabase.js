const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Configurações do Supabase não encontradas no .env');
    process.exit(1);
}

// Criar cliente Supabase com service role key para operações administrativas
const supabase = createClient(supabaseUrl, supabaseServiceKey);

class SupabaseLicenseManager {
    constructor() {
        this.supabase = supabase;
        console.log('✅ SupabaseLicenseManager inicializado');
    }

    // Criar usuário se não existir
    async createOrGetUser(discordId, username, email = null) {
        try {
            // Verificar se usuário já existe
            const { data: existingUser, error: findError } = await this.supabase
                .from('users')
                .select('*')
                .eq('discord_id', discordId)
                .single();

            if (existingUser) {
                // Atualizar username se mudou
                if (existingUser.username !== username) {
                    const { data: updatedUser, error: updateError } = await this.supabase
                        .from('users')
                        .update({ username, email })
                        .eq('discord_id', discordId)
                        .select()
                        .single();

                    if (updateError) throw updateError;
                    return updatedUser;
                }
                return existingUser;
            }

            // Criar novo usuário
            const { data: newUser, error: createError } = await this.supabase
                .from('users')
                .insert({
                    discord_id: discordId,
                    username,
                    email
                })
                .select()
                .single();

            if (createError) throw createError;
            return newUser;
        } catch (error) {
            console.error('Erro ao criar/buscar usuário:', error);
            throw error;
        }
    }

    // Criar produto se não existir
    async createOrGetProduct(name, version, description = null, price = null, downloadLink = null) {
        try {
            // Verificar se produto já existe
            const { data: existingProduct, error: findError } = await this.supabase
                .from('products')
                .select('*')
                .eq('name', name)
                .eq('version', version)
                .single();

            if (existingProduct) {
                console.log(`📦 Produto encontrado: ${existingProduct.name} (ID: ${existingProduct.id})`);
                return existingProduct;
            }

            // Criar novo produto
            const productData = {
                name,
                version,
                description,
                price
            };

            // Adicionar download_link se fornecido
            if (downloadLink) {
                productData.download_link = downloadLink;
            }

            const { data: newProduct, error: createError } = await this.supabase
                .from('products')
                .insert(productData)
                .select()
                .single();

            if (createError) throw createError;

            console.log(`📦 Produto criado: ${newProduct.name} (ID: ${newProduct.id})`);
            return newProduct;
        } catch (error) {
            console.error('Erro ao criar/buscar produto:', error);
            throw error;
        }
    }

    // Buscar produto por ID
    async getProductById(productId) {
        try {
            console.log(`🔍 Buscando produto por ID: ${productId}`);

            const { data: product, error } = await this.supabase
                .from('products')
                .select('*')
                .eq('id', productId)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                console.error('❌ Erro na consulta Supabase:', error);
                throw error;
            }

            if (product) {
                console.log(`✅ Produto encontrado: ${product.name} (ID: ${product.id})`);
            } else {
                console.log(`⚠️ Produto não encontrado com ID: ${productId}`);
            }

            return product;

        } catch (error) {
            console.error('❌ Erro ao buscar produto por ID:', error);
            throw error;
        }
    }

    // Buscar todos os produtos
    async getAllProducts() {
        try {
            const { data: products, error } = await this.supabase
                .from('products')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            return products || [];

        } catch (error) {
            console.error('❌ Erro ao buscar todos os produtos:', error);
            throw error;
        }
    }

    // Deletar produto por ID
    async deleteProduct(productId) {
        try {
            console.log(`🗑️ Deletando produto por ID: ${productId}`);

            const { data, error } = await this.supabase
                .from('products')
                .delete()
                .eq('id', productId)
                .select();

            if (error) {
                throw error;
            }

            if (data && data.length > 0) {
                console.log(`✅ Produto deletado: ${data[0].name} (ID: ${data[0].id})`);
                return true;
            } else {
                console.log(`⚠️ Produto não encontrado para deletar: ${productId}`);
                return false;
            }

        } catch (error) {
            console.error('❌ Erro ao deletar produto:', error);
            throw error;
        }
    }

    // Atualizar produto por ID
    async updateProduct(productId, updateData) {
        try {
            console.log(`✏️ Atualizando produto por ID: ${productId}`);
            console.log(`📝 Dados para atualizar:`, updateData);

            // Preparar dados de atualização - aceitar tanto 'name' quanto 'title'
            const updateFields = {
                updated_at: new Date().toISOString()
            };

            if (updateData.name) updateFields.name = updateData.name;
            if (updateData.title) updateFields.name = updateData.title;
            if (updateData.description) updateFields.description = updateData.description;
            if (updateData.price !== undefined) updateFields.price = updateData.price;
            if (updateData.version) updateFields.version = updateData.version;
            if (updateData.download_link) updateFields.download_link = updateData.download_link;

            console.log(`📝 Campos a serem atualizados:`, updateFields);

            const { data, error } = await this.supabase
                .from('products')
                .update(updateFields)
                .eq('id', productId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            if (data) {
                console.log(`✅ Produto atualizado: ${data.name} (ID: ${data.id})`);
                return data;
            } else {
                console.log(`⚠️ Produto não encontrado para atualizar: ${productId}`);
                return null;
            }

        } catch (error) {
            console.error('❌ Erro ao atualizar produto:', error);
            throw error;
        }
    }

    // Criar venda temporária
    async createSale(saleData) {
        try {
            console.log(`💳 Criando venda no Supabase: ${saleData.id}`);

            const { data, error } = await this.supabase
                .from('sales')
                .insert({
                    id: saleData.id,
                    user_id: saleData.user_id,
                    username: saleData.username,
                    product_id: saleData.product_id,
                    amount: saleData.amount,
                    channel_id: saleData.channel_id,
                    status: saleData.status || 'pending',
                    payment_method: saleData.payment_method || 'pix',
                    created_at: new Date().toISOString()
                })
                .select()
                .single();

            if (error) {
                throw error;
            }

            console.log(`✅ Venda criada no Supabase: ${data.id}`);
            return data;

        } catch (error) {
            console.error('❌ Erro ao criar venda no Supabase:', error);
            throw error;
        }
    }

    // Buscar venda por ID
    async getSale(saleId) {
        try {
            console.log(`🔍 Buscando venda por ID: ${saleId}`);

            const { data: sale, error } = await this.supabase
                .from('sales')
                .select('*')
                .eq('id', saleId)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                throw error;
            }

            if (sale) {
                console.log(`✅ Venda encontrada: ${sale.id} - ${sale.username}`);
            } else {
                console.log(`⚠️ Venda não encontrada: ${saleId}`);
            }

            return sale;

        } catch (error) {
            console.error('❌ Erro ao buscar venda:', error);
            throw error;
        }
    }

    // Buscar venda por channel_id
    async getSaleByChannelId(channelId) {
        try {
            console.log(`🔍 Buscando venda por channel_id: ${channelId}`);

            const { data: sale, error } = await this.supabase
                .from('sales')
                .select('*')
                .eq('channel_id', channelId)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                throw error;
            }

            if (sale) {
                console.log(`✅ Venda encontrada por channel: ${sale.id}`);
            } else {
                console.log(`⚠️ Venda não encontrada para channel: ${channelId}`);
            }

            return sale;

        } catch (error) {
            console.error('❌ Erro ao buscar venda por channel:', error);
            throw error;
        }
    }

    // Atualizar status da venda
    async updateSaleStatus(saleId, status, paymentMethod = null) {
        try {
            console.log(`📝 Atualizando status da venda: ${saleId} -> ${status}`);

            const updateData = {
                status: status,
                updated_at: new Date().toISOString()
            };

            if (paymentMethod) {
                updateData.payment_method = paymentMethod;
            }

            const { data, error } = await this.supabase
                .from('sales')
                .update(updateData)
                .eq('id', saleId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            if (data) {
                console.log(`✅ Status da venda atualizado: ${data.id} -> ${data.status}`);
                return data;
            } else {
                console.log(`⚠️ Venda não encontrada para atualizar: ${saleId}`);
                return null;
            }

        } catch (error) {
            console.error('❌ Erro ao atualizar status da venda:', error);
            throw error;
        }
    }

    // Deletar venda por ID
    async deleteSale(saleId) {
        try {
            console.log(`🗑️ Deletando venda por ID: ${saleId}`);

            const { data, error } = await this.supabase
                .from('sales')
                .delete()
                .eq('id', saleId)
                .select();

            if (error) {
                throw error;
            }

            if (data && data.length > 0) {
                console.log(`✅ Venda deletada: ${data[0].username} - R$ ${data[0].amount}`);
                return true;
            } else {
                console.log(`⚠️ Venda não encontrada para deletar: ${saleId}`);
                return false;
            }

        } catch (error) {
            console.error('❌ Erro ao deletar venda:', error);
            throw error;
        }
    }

    // Limpar vendas antigas pendentes (mais de 24 horas)
    async cleanupOldSales() {
        try {
            console.log('🧹 Limpando vendas antigas pendentes...');

            // Calcular data de 24 horas atrás
            const twentyFourHoursAgo = new Date();
            twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

            const { data, error } = await this.supabase
                .from('sales')
                .delete()
                .eq('status', 'pending')
                .lt('created_at', twentyFourHoursAgo.toISOString())
                .select();

            if (error) {
                throw error;
            }

            if (data && data.length > 0) {
                console.log(`✅ ${data.length} vendas antigas removidas:`);
                data.forEach(sale => {
                    console.log(`   - ${sale.username} - R$ ${sale.amount} (${sale.created_at})`);
                });
            } else {
                console.log('ℹ️ Nenhuma venda antiga encontrada para limpeza');
            }

            return data ? data.length : 0;

        } catch (error) {
            console.error('❌ Erro ao limpar vendas antigas:', error);
            throw error;
        }
    }

    // Criar licença
    async createLicense(licenseKey, userId, productId, expiresAt = null) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .insert({
                    license_key: licenseKey,
                    user_id: userId,
                    product_id: productId,
                    expires_at: expiresAt,
                    status: 'active'
                })
                .select(`
                    *,
                    users:user_id(discord_id, username),
                    products:product_id(name, version)
                `)
                .single();

            if (error) throw error;
            return license;
        } catch (error) {
            console.error('Erro ao criar licença:', error);
            throw error;
        }
    }

    // Validar licença
    async validateLicense(licenseKey, serverIp = null, serverPort = null, userAgent = null) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .select(`
                    *,
                    users:user_id(discord_id, username),
                    products:product_id(name, version)
                `)
                .eq('license_key', licenseKey)
                .single();

            if (error || !license) {
                await this.logValidation(null, serverIp, serverPort, userAgent, false, 'Licença não encontrada');
                return { valid: false, error: 'Licença não encontrada' };
            }

            // Verificar se a licença está ativa
            if (license.status !== 'active') {
                await this.logValidation(license.id, serverIp, serverPort, userAgent, false, 'Licença não está ativa');
                return { valid: false, error: 'Licença não está ativa', license };
            }

            // Verificar se a licença expirou
            if (license.expires_at && new Date(license.expires_at) < new Date()) {
                await this.logValidation(license.id, serverIp, serverPort, userAgent, false, 'Licença expirada');
                return { valid: false, error: 'Licença expirada', license };
            }

            // Atualizar última validação
            await this.supabase
                .from('licenses')
                .update({
                    last_validation: new Date().toISOString(),
                    validation_count: license.validation_count + 1,
                    server_ip: serverIp || license.server_ip,
                    server_port: serverPort || license.server_port
                })
                .eq('id', license.id);

            // Log da validação bem-sucedida
            await this.logValidation(license.id, serverIp, serverPort, userAgent, true);

            return { valid: true, license };
        } catch (error) {
            console.error('Erro ao validar licença:', error);
            await this.logValidation(null, serverIp, serverPort, userAgent, false, error.message);
            return { valid: false, error: 'Erro interno do servidor' };
        }
    }

    // Ativar licença em servidor
    async activateLicense(licenseKey, serverName, serverIp, serverPort) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .update({
                    server_name: serverName,
                    server_ip: serverIp,
                    server_port: serverPort,
                    last_validation: new Date().toISOString()
                })
                .eq('license_key', licenseKey)
                .eq('status', 'active')
                .select(`
                    *,
                    users:user_id(discord_id, username),
                    products:product_id(name, version)
                `)
                .single();

            if (error) throw error;
            return license;
        } catch (error) {
            console.error('Erro ao ativar licença:', error);
            throw error;
        }
    }

    // Listar licenças ativas
    async getActiveLicenses(page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            
            const { data: licenses, error, count } = await this.supabase
                .from('licenses')
                .select(`
                    *,
                    users:user_id(discord_id, username),
                    products:product_id(name, version)
                `, { count: 'exact' })
                .eq('status', 'active')
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) throw error;

            return {
                licenses,
                total: count,
                page,
                limit,
                totalPages: Math.ceil(count / limit)
            };
        } catch (error) {
            console.error('Erro ao listar licenças:', error);
            throw error;
        }
    }

    // Buscar licença por chave
    async getLicenseByKey(licenseKey) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .select(`
                    *,
                    users:user_id(discord_id, username),
                    products:product_id(name, version)
                `)
                .eq('license_key', licenseKey)
                .single();

            if (error) throw error;
            return license;
        } catch (error) {
            console.error('Erro ao buscar licença:', error);
            throw error;
        }
    }

    // Suspender licença
    async suspendLicense(licenseKey) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .update({ status: 'suspended' })
                .eq('license_key', licenseKey)
                .select()
                .single();

            if (error) throw error;
            return license;
        } catch (error) {
            console.error('Erro ao suspender licença:', error);
            throw error;
        }
    }

    // Reativar licença
    async reactivateLicense(licenseKey) {
        try {
            const { data: license, error } = await this.supabase
                .from('licenses')
                .update({ status: 'active' })
                .eq('license_key', licenseKey)
                .select()
                .single();

            if (error) throw error;
            return license;
        } catch (error) {
            console.error('Erro ao reativar licença:', error);
            throw error;
        }
    }

    // Log de validação
    async logValidation(licenseId, serverIp, serverPort, userAgent, success, errorMessage = null) {
        try {
            await this.supabase
                .from('license_validations')
                .insert({
                    license_id: licenseId,
                    server_ip: serverIp,
                    server_port: serverPort,
                    user_agent: userAgent,
                    success,
                    error_message: errorMessage
                });
        } catch (error) {
            console.error('Erro ao registrar log de validação:', error);
        }
    }

    // Estatísticas
    async getStats() {
        try {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // Licenças ativas
            const { count: activeLicenses } = await this.supabase
                .from('licenses')
                .select('*', { count: 'exact', head: true })
                .eq('status', 'active')
                .or(`expires_at.is.null,expires_at.gt.${now.toISOString()}`);

            // Validações hoje
            const { count: validationsToday } = await this.supabase
                .from('license_validations')
                .select('*', { count: 'exact', head: true })
                .gte('created_at', today.toISOString())
                .eq('success', true);

            // Servidores únicos conectados
            const { data: uniqueServers } = await this.supabase
                .from('licenses')
                .select('server_ip')
                .eq('status', 'active')
                .not('server_ip', 'is', null)
                .not('last_validation', 'is', null);

            const connectedServers = new Set(uniqueServers?.map(s => s.server_ip) || []).size;

            return {
                activeLicenses: activeLicenses || 0,
                validationsToday: validationsToday || 0,
                connectedServers,
                uptime: '99.9%'
            };
        } catch (error) {
            console.error('Erro ao buscar estatísticas:', error);
            return {
                activeLicenses: 0,
                validationsToday: 0,
                connectedServers: 0,
                uptime: '99.9%'
            };
        }
    }

    // Migrar dados do SQLite para Supabase
    async migrateSQLiteData(sqliteDb) {
        try {
            console.log('🔄 Iniciando migração de dados do SQLite para Supabase...');

            // Migrar usuários
            const users = sqliteDb.prepare('SELECT * FROM users').all();
            for (const user of users) {
                await this.createOrGetUser(user.discord_id, user.username, user.email);
            }
            console.log(`✅ Migrados ${users.length} usuários`);

            // Migrar produtos (assumindo que existem)
            const products = sqliteDb.prepare('SELECT DISTINCT product_name, product_version FROM licenses').all();
            const productMap = new Map();
            for (const product of products) {
                const supabaseProduct = await this.createOrGetProduct(
                    product.product_name || 'StonePlugin',
                    product.product_version || '1.0.0'
                );
                productMap.set(`${product.product_name}-${product.product_version}`, supabaseProduct.id);
            }
            console.log(`✅ Migrados ${products.length} produtos`);

            // Migrar licenças
            const licenses = sqliteDb.prepare(`
                SELECT l.*, u.discord_id 
                FROM licenses l 
                LEFT JOIN users u ON l.user_id = u.id
            `).all();

            for (const license of licenses) {
                try {
                    // Buscar usuário no Supabase
                    const { data: supabaseUser } = await this.supabase
                        .from('users')
                        .select('id')
                        .eq('discord_id', license.discord_id)
                        .single();

                    if (!supabaseUser) continue;

                    // Buscar produto no Supabase
                    const productKey = `${license.product_name || 'StonePlugin'}-${license.product_version || '1.0.0'}`;
                    const productId = productMap.get(productKey);

                    if (!productId) continue;

                    // Criar licença no Supabase
                    await this.supabase
                        .from('licenses')
                        .upsert({
                            license_key: license.license_key,
                            user_id: supabaseUser.id,
                            product_id: productId,
                            status: license.status || 'active',
                            server_name: license.server_name,
                            server_ip: license.server_ip,
                            server_port: license.server_port,
                            expires_at: license.expires_at,
                            last_validation: license.last_validation,
                            validation_count: license.validation_count || 0,
                            created_at: license.created_at || new Date().toISOString()
                        }, { onConflict: 'license_key' });

                } catch (error) {
                    console.error(`Erro ao migrar licença ${license.license_key}:`, error);
                }
            }
            console.log(`✅ Migradas ${licenses.length} licenças`);

            console.log('🎉 Migração concluída com sucesso!');
        } catch (error) {
            console.error('❌ Erro durante a migração:', error);
            throw error;
        }
    }

    // Salvar mensagem publicada
    async savePublishedMessage(messageData) {
        try {
            console.log(`💾 Salvando mensagem publicada: ${messageData.message_id}`);
            
            const { data, error } = await this.supabase
                .from('published_messages')
                .upsert({
                    message_id: messageData.message_id,
                    channel_id: messageData.channel_id,
                    product_id: messageData.product_id,
                    guild_id: messageData.guild_id
                }, {
                    onConflict: 'message_id'
                });

            if (error) {
                // Se a tabela não existir, mostrar instruções
                if (error.code === '42P01') {
                    console.error('❌ Tabela published_messages não existe!');
                    console.log('💡 Execute este SQL no painel do Supabase:');
                    console.log(`
CREATE TABLE IF NOT EXISTS published_messages (
    id SERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    channel_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    guild_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_channel_id ON published_messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_published_messages_guild_id ON published_messages(guild_id);
                    `);
                }
                throw error;
            }
            
            console.log(`✅ Mensagem publicada salva com sucesso: ${messageData.message_id}`);
            return data;
        } catch (error) {
            console.error('Erro ao salvar mensagem publicada no Supabase:', error);
            throw error;
        }
    }

    // Buscar mensagens publicadas de um produto
    async getPublishedMessages(productId) {
        try {
            console.log(`🔍 Buscando mensagens publicadas para produto: ${productId}`);
            
            const { data, error } = await this.supabase
                .from('published_messages')
                .select('*')
                .eq('product_id', productId)
                .order('created_at', { ascending: false });

            if (error) {
                if (error.code === '42P01') {
                    console.error('❌ Tabela published_messages não existe!');
                    console.log('💡 Execute o SQL no painel do Supabase para criar a tabela.');
                }
                throw error;
            }
            
            console.log(`📨 Encontradas ${data?.length || 0} mensagens publicadas`);
            return data || [];
        } catch (error) {
            console.error('Erro ao buscar mensagens publicadas no Supabase:', error);
            return [];
        }
    }

    // Remover mensagem publicada (quando deletada)
    async removePublishedMessage(messageId) {
        try {
            const { data, error } = await this.supabase
                .from('published_messages')
                .delete()
                .eq('message_id', messageId);

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Erro ao remover mensagem publicada no Supabase:', error);
            throw error;
        }
    }

    // Remover todas as mensagens publicadas de um produto
    async removePublishedMessagesForProduct(productId) {
        try {
            const { data, error } = await this.supabase
                .from('published_messages')
                .delete()
                .eq('product_id', productId);

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Erro ao remover mensagens publicadas do produto no Supabase:', error);
            throw error;
        }
    }

    // Salvar configuração do produto
    async saveProductConfig(productId, key, value) {
        try {
            console.log(`💾 Salvando configuração: ${key} = ${value} para produto ${productId}`);
            
            const { data, error } = await this.supabase
                .from('product_configs')
                .upsert({
                    product_id: productId,
                    config_key: key,
                    config_value: value,
                    updated_at: new Date().toISOString()
                }, {
                    onConflict: 'product_id,config_key'
                });

            if (error) {
                console.error('❌ Erro detalhado do Supabase:', error);
                
                if (error.code === '42P01') {
                    console.error('❌ Tabela product_configs não existe!');
                    console.log('💡 SOLUÇÃO: Execute este comando:');
                    console.log('node setup-supabase-tables.js');
                    console.log('💡 OU execute este SQL no painel do Supabase:');
                    console.log(`
CREATE TABLE IF NOT EXISTS product_configs (
    id SERIAL PRIMARY KEY,
    product_id TEXT NOT NULL,
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, config_key)
);

CREATE INDEX IF NOT EXISTS idx_product_configs_product_id ON product_configs(product_id);
                    `);
                } else {
                    console.error('❌ Erro não relacionado à tabela:', error.message);
                    console.error('❌ Código do erro:', error.code);
                    console.error('❌ Detalhes:', error.details);
                }
                
                throw new Error(`Erro ao salvar configuração: ${error.message} (Código: ${error.code})`);
            }
            
            console.log(`✅ Configuração salva com sucesso`);
            return data;
        } catch (error) {
            console.error('Erro ao salvar configuração do produto no Supabase:', error);
            throw error;
        }
    }

    // Buscar configurações do produto
    async getProductConfig(productId) {
        try {
            console.log(`🔍 Buscando configurações para produto: ${productId}`);
            
            const { data, error } = await this.supabase
                .from('product_configs')
                .select('config_key, config_value')
                .eq('product_id', productId);

            if (error) {
                if (error.code === '42P01') {
                    console.error('❌ Tabela product_configs não existe!');
                    return {};
                }
                throw error;
            }
            
            const config = {};
            data.forEach(row => {
                config[row.config_key] = row.config_value;
            });
            
            console.log(`📋 Configurações encontradas:`, config);
            return config;
        } catch (error) {
            console.error('Erro ao buscar configurações do produto no Supabase:', error);
            return {};
        }
    }
}

module.exports = { SupabaseLicenseManager, supabase };