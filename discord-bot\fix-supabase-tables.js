const { SupabaseLicenseManager } = require('./supabase');
const fs = require('fs');
const path = require('path');

async function fixSupabaseTables() {
    try {
        console.log('🔧 Corrigindo tabelas do Supabase...');
        
        const supabaseLicenseManager = new SupabaseLicenseManager();
        
        // Ler o SQL completo
        const sqlPath = path.join(__dirname, 'create-supabase-tables-complete.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        console.log('📋 SQL a ser executado:');
        console.log('----------------------------------------');
        console.log(sql);
        console.log('----------------------------------------');
        
        // Tentar executar via RPC (se disponível)
        try {
            console.log('🔄 Tentando executar via RPC...');
            const { data, error } = await supabaseLicenseManager.supabase.rpc('exec_sql', { 
                sql_query: sql 
            });
            
            if (error) {
                console.error('❌ RPC falhou:', error);
                throw error;
            }
            
            console.log('✅ Tabelas criadas via RPC com sucesso!');
            console.log('📊 Resultado:', data);
            
        } catch (rpcError) {
            console.log('⚠️ RPC não disponível, tentando método alternativo...');
            
            // Executar comandos individuais
            await createTablesIndividually(supabaseLicenseManager);
        }
        
        // Testar as tabelas
        await testTables(supabaseLicenseManager);
        
        console.log('🎉 Correção concluída!');
        
    } catch (error) {
        console.error('❌ Erro durante a correção:', error);
        console.log('');
        console.log('🚨 SOLUÇÃO MANUAL NECESSÁRIA:');
        console.log('1. Acesse: https://supabase.com/dashboard');
        console.log('2. Vá em: SQL Editor');
        console.log('3. Execute o conteúdo do arquivo: create-supabase-tables-complete.sql');
        console.log('');
    }
}

async function createTablesIndividually(supabaseLicenseManager) {
    console.log('🔧 Criando tabelas individualmente...');
    
    // Comandos SQL individuais
    const commands = [
        `CREATE TABLE IF NOT EXISTS published_messages (
            id SERIAL PRIMARY KEY,
            message_id TEXT NOT NULL UNIQUE,
            channel_id TEXT NOT NULL,
            product_id TEXT NOT NULL,
            guild_id TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )`,
        
        `CREATE TABLE IF NOT EXISTS product_configs (
            id SERIAL PRIMARY KEY,
            product_id TEXT NOT NULL,
            config_key TEXT NOT NULL,
            config_value TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(product_id, config_key)
        )`,
        
        `CREATE INDEX IF NOT EXISTS idx_published_messages_product_id ON published_messages(product_id)`,
        `CREATE INDEX IF NOT EXISTS idx_product_configs_product_id ON product_configs(product_id)`
    ];
    
    for (const command of commands) {
        try {
            console.log(`🔄 Executando: ${command.substring(0, 50)}...`);
            const { data, error } = await supabaseLicenseManager.supabase.rpc('exec_sql', { 
                sql_query: command 
            });
            
            if (error) {
                console.error(`❌ Erro no comando: ${error.message}`);
            } else {
                console.log(`✅ Comando executado com sucesso`);
            }
        } catch (error) {
            console.error(`❌ Erro ao executar comando: ${error.message}`);
        }
    }
}

async function testTables(supabaseLicenseManager) {
    console.log('🧪 Testando tabelas...');
    
    // Testar published_messages
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('published_messages')
            .select('count')
            .limit(1);
            
        if (error) {
            console.error('❌ Tabela published_messages não funciona:', error.message);
        } else {
            console.log('✅ Tabela published_messages OK');
        }
    } catch (error) {
        console.error('❌ Erro ao testar published_messages:', error.message);
    }
    
    // Testar product_configs
    try {
        const { data, error } = await supabaseLicenseManager.supabase
            .from('product_configs')
            .select('count')
            .limit(1);
            
        if (error) {
            console.error('❌ Tabela product_configs não funciona:', error.message);
        } else {
            console.log('✅ Tabela product_configs OK');
        }
    } catch (error) {
        console.error('❌ Erro ao testar product_configs:', error.message);
    }
    
    // Testar inserção em published_messages
    try {
        console.log('🧪 Testando inserção em published_messages...');
        const testData = {
            message_id: 'test_' + Date.now(),
            channel_id: 'test_channel',
            product_id: 'test_product',
            guild_id: 'test_guild'
        };
        
        const { data, error } = await supabaseLicenseManager.supabase
            .from('published_messages')
            .insert(testData)
            .select();
            
        if (error) {
            console.error('❌ Erro ao inserir teste:', error.message);
        } else {
            console.log('✅ Inserção de teste OK');
            
            // Remover teste
            await supabaseLicenseManager.supabase
                .from('published_messages')
                .delete()
                .eq('message_id', testData.message_id);
                
            console.log('✅ Teste removido');
        }
    } catch (error) {
        console.error('❌ Erro no teste de inserção:', error.message);
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    fixSupabaseTables()
        .then(() => {
            console.log('✅ Script finalizado');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Erro no script:', error);
            process.exit(1);
        });
}

module.exports = { fixSupabaseTables };