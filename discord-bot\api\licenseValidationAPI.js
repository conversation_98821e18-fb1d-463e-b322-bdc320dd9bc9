const express = require('express');
const { SupabaseLicenseManager } = require('../supabase');
const Utils = require('../utils');

class LicenseValidationAPI {
    constructor(database) {
        this.database = database;
        this.supabaseLicenseManager = new SupabaseLicenseManager();
        this.router = express.Router();
        this.setupRoutes();
    }

    setupRoutes() {
        // Endpoint para ativar licença
        this.router.post('/activate', async (req, res) => {
            try {
                const { license_key, server_ip, server_port, server_uuid } = req.body;

                if (!license_key || !server_uuid) {
                    return res.status(400).json({
                        success: false,
                        error: 'license_key e server_uuid são obrigatórios'
                    });
                }

                console.log(`🔑 Tentativa de ativação de licença: ${license_key.substring(0, 8)}...`);
                console.log(`🖥️ Servidor: ${server_uuid} (${server_ip}:${server_port})`);

                // Buscar licença no banco
                const license = await this.findLicense(license_key);
                if (!license) {
                    return res.status(404).json({
                        success: false,
                        error: 'Licença não encontrada'
                    });
                }

                // Verificar se licença já está ativa em outro servidor
                if (license.server_uuid && license.server_uuid !== server_uuid) {
                    return res.status(409).json({
                        success: false,
                        error: 'Licença já está ativa em outro servidor'
                    });
                }

                // Verificar se licença está expirada
                if (this.isLicenseExpired(license)) {
                    return res.status(410).json({
                        success: false,
                        error: 'Licença expirada'
                    });
                }

                // Ativar licença
                await this.activateLicense(license_key, server_uuid, server_ip, server_port);

                // Buscar informações do produto
                const product = await this.getProductInfo(license.product_id);

                console.log(`✅ Licença ativada com sucesso: ${product?.name || 'Produto desconhecido'}`);

                res.json({
                    success: true,
                    message: 'Licença ativada com sucesso',
                    product_name: product?.name || 'Produto',
                    expires_at: license.expires_at,
                    activated_at: new Date().toISOString()
                });

            } catch (error) {
                console.error('❌ Erro ao ativar licença:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // Endpoint para validar licença
        this.router.post('/validate', async (req, res) => {
            try {
                const { license_key, server_uuid } = req.body;

                if (!license_key || !server_uuid) {
                    return res.status(400).json({
                        success: false,
                        error: 'license_key e server_uuid são obrigatórios'
                    });
                }

                // Buscar licença
                const license = await this.findLicense(license_key);
                if (!license) {
                    return res.status(404).json({
                        success: false,
                        error: 'Licença não encontrada'
                    });
                }

                // Verificar se licença pertence ao servidor
                if (license.server_uuid !== server_uuid) {
                    return res.status(403).json({
                        success: false,
                        error: 'Licença não pertence a este servidor'
                    });
                }

                // Verificar se licença está expirada
                if (this.isLicenseExpired(license)) {
                    return res.status(410).json({
                        success: false,
                        error: 'Licença expirada'
                    });
                }

                // Atualizar último uso
                await this.updateLastUsed(license_key);

                res.json({
                    success: true,
                    valid: true,
                    expires_at: license.expires_at
                });

            } catch (error) {
                console.error('❌ Erro ao validar licença:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // Endpoint para verificar atualizações
        this.router.post('/update-check', async (req, res) => {
            try {
                const { license_key, current_version } = req.body;

                // Buscar licença
                const license = await this.findLicense(license_key);
                if (!license) {
                    return res.status(404).json({
                        success: false,
                        error: 'Licença não encontrada'
                    });
                }

                // Buscar informações do produto
                const product = await this.getProductInfo(license.product_id);
                if (!product) {
                    return res.status(404).json({
                        success: false,
                        error: 'Produto não encontrado'
                    });
                }

                const hasUpdate = product.version !== current_version;

                res.json({
                    success: true,
                    has_update: hasUpdate,
                    latest_version: product.version,
                    current_version: current_version,
                    download_url: hasUpdate ? product.download_link : null
                });

            } catch (error) {
                console.error('❌ Erro ao verificar atualizações:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // Endpoint para notificar desligamento do servidor
        this.router.post('/server/shutdown', async (req, res) => {
            try {
                const { server_uuid } = req.body;

                if (server_uuid) {
                    console.log(`🔌 Servidor desconectado: ${server_uuid}`);
                    // Aqui você pode implementar lógica adicional se necessário
                }

                res.json({ success: true });

            } catch (error) {
                console.error('❌ Erro ao processar desligamento:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor'
                });
            }
        });

        // Endpoint de health check
        this.router.get('/health', (req, res) => {
            res.json({
                success: true,
                status: 'online',
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * Buscar licença no banco de dados
     */
    async findLicense(licenseKey) {
        try {
            if (this.database.isDatabaseAvailable()) {
                // Buscar no SQLite/PostgreSQL local
                const result = await this.database.query(
                    'SELECT * FROM sales WHERE id = ? AND status = ?',
                    [licenseKey, 'approved']
                );
                return result[0] || null;
            } else {
                // Buscar no Supabase
                const result = await this.supabaseLicenseManager.getLicenseByKey(licenseKey);
                return result;
            }
        } catch (error) {
            console.error('❌ Erro ao buscar licença:', error);
            return null;
        }
    }

    /**
     * Verificar se licença está expirada
     */
    isLicenseExpired(license) {
        if (!license.expires_at) {
            return false; // Licença vitalícia
        }

        const expiryDate = new Date(license.expires_at);
        return new Date() > expiryDate;
    }

    /**
     * Ativar licença
     */
    async activateLicense(licenseKey, serverUUID, serverIP, serverPort) {
        try {
            const activationData = {
                server_uuid: serverUUID,
                server_ip: serverIP,
                server_port: serverPort,
                activated_at: new Date().toISOString(),
                last_used: new Date().toISOString()
            };

            if (this.database.isDatabaseAvailable()) {
                // Atualizar no banco local
                await this.database.query(
                    'UPDATE sales SET server_uuid = ?, server_ip = ?, server_port = ?, activated_at = ?, last_used = ? WHERE id = ?',
                    [serverUUID, serverIP, serverPort, activationData.activated_at, activationData.last_used, licenseKey]
                );
            } else {
                // Atualizar no Supabase
                await this.supabaseLicenseManager.activateLicense(licenseKey, activationData);
            }

            console.log(`✅ Licença ativada: ${licenseKey.substring(0, 8)}... para servidor ${serverUUID}`);

        } catch (error) {
            console.error('❌ Erro ao ativar licença:', error);
            throw error;
        }
    }

    /**
     * Atualizar último uso da licença
     */
    async updateLastUsed(licenseKey) {
        try {
            const lastUsed = new Date().toISOString();

            if (this.database.isDatabaseAvailable()) {
                await this.database.query(
                    'UPDATE sales SET last_used = ? WHERE id = ?',
                    [lastUsed, licenseKey]
                );
            } else {
                await this.supabaseLicenseManager.updateLastUsed(licenseKey, lastUsed);
            }

        } catch (error) {
            console.error('❌ Erro ao atualizar último uso:', error);
        }
    }

    /**
     * Buscar informações do produto
     */
    async getProductInfo(productId) {
        try {
            if (this.database.isDatabaseAvailable()) {
                const result = await this.database.query(
                    'SELECT * FROM products WHERE id = ?',
                    [productId]
                );
                return result[0] || null;
            } else {
                return await this.supabaseLicenseManager.getProductById(productId);
            }
        } catch (error) {
            console.error('❌ Erro ao buscar produto:', error);
            return null;
        }
    }

    getRouter() {
        return this.router;
    }
}

module.exports = LicenseValidationAPI;