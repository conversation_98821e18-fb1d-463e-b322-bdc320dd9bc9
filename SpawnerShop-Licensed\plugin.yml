name: SpawnerShop
version: 2.0.0
main: com.stoneplugins.spawnershop.SpawnerShopLicensed
author: StonePlugins
description: Sistema de loja de spawners com licenciamento integrado
website: https://stoneplugins.com
api-version: 1.13

# DEPENDÊNCIA OBRIGATÓRIA
depend: [StonePlugins-Core]

commands:
  spawnershop:
    description: Comandos principais do SpawnerShop
    usage: /spawnershop [shop|info|license|admin]
    aliases: [ss, spawner]

permissions:
  spawnershop.use:
    description: Permissão básica para usar a loja
    default: true
  spawnershop.admin:
    description: Permissão de administrador
    default: op
  spawnershop.buy:
    description: Permissão para comprar spawners
    default: true

# Configurações extras para o sistema de licenças
extra:
  required-license: "SpawnerShop"
  license-required: true
  demo-mode: true