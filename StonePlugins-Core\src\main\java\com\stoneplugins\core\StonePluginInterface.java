package com.stoneplugins.core;

/**
 * Interface que todos os plugins StonePlugins devem implementar
 * para receber notificações sobre ativação de licenças
 */
public interface StonePluginInterface {
    
    /**
     * <PERSON><PERSON><PERSON> quando a licença do plugin é ativada
     */
    void onLicenseActivated();
    
    /**
     * Cha<PERSON><PERSON> quando a licença do plugin é desativada
     */
    default void onLicenseDeactivated() {
        // Implementação padrão vazia
    }
    
    /**
     * Verificar se o plugin pode funcionar sem licença (modo demo)
     */
    default boolean canRunWithoutLicense() {
        return false;
    }
    
    /**
     * Obter nome do produto/licença requerida
     */
    String getRequiredLicenseName();
}